// 生成随机字符串
export function generateRandomString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~'
    return Array.from(crypto.getRandomValues(new Uint8Array(length)))
      .map(byte => chars[byte % chars.length])
      .join('')
  }
  
  // 生成 PKCE code_challenge
  export async function generateCode<PERSON>hallenge(codeVerifier) {
    const encoder = new TextEncoder()
    const data = encoder.encode(codeVerifier)
    const digest = await crypto.subtle.digest('SHA-256', data)
    return btoa(String.fromCharCode(...new Uint8Array(digest)))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '')
  }