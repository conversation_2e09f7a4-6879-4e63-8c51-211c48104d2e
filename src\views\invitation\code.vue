<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router';
import QRCode from 'qrcode';
const router = useRouter()
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
import config from '@/utils/env.js'
const goBack = () => {
    router.go(-1)
}
const qrCanvas = ref(null);
const text = localStorage.getItem('id') ? `${config.copyUrl}/invitation?id=${localStorage.getItem('id')}` : `${config.copyUrl}/invitation`;

onMounted(() => {
    QRCode.toCanvas(qrCanvas.value, text, error => {
        if (error) console.error(error);
    });
});
</script>

<template>
    <div class="container ">
        <div class="con">
            <div class="top">
                <div class="left" @click="goBack"><img src="@/assets/images/active/back (2).png" alt=""></div>
            </div>
            <div class="main">
                <canvas ref="qrCanvas"></canvas>
            </div>

        </div>
    </div>
</template>
<style scoped lang="scss">
.container {
    padding-right: 40px;
    padding-left: 40px;
    background: linear-gradient(180deg, #193572 0%, #4D8FEF 100%), #437CD2;
    height: 100%;

    .con {
        display: flex;
        flex-direction: column;
        height: 100%;
        align-items: center;
        font-size: 30px;

        .top {
            width: 100%;
            height: 88px;
            display: flex;
            margin-bottom: 34px;
            font-size: var(--fontSize22);
            align-items: center;

            .left {
                img {
                    width: 48px;
                    height: 48px;
                }
            }


        }

        .main {
            margin-top: 68px;
            margin-bottom: 68px;
            margin-left: 58px;
            margin-right: 58px;
            width: 634px;
            height: 634px;
            background: #FFFFFF;
            border-radius: 32px;

            canvas {
                width: 100% !important;
                height: 100% !important;
            }
        }
    }
}

// @media (max-width: 319px) {
//     .main{
//         width: 290px !important;
//         height: 290px !important;
//     }
// }
// @media screen and (min-width: 320px) and (max-width: 414px) {
//     .main{
//         width: 300px !important;
//         height: 300px !important;
//     }
// }
// @media screen and (min-width: 415px) and (max-width: 590px) {
//     .main{
//         width: 334px !important;
//         height: 334px !important;
//     }
// }
// @media screen and (min-width: 591px) and (max-width: 749px) {
//     .main{
//         width: 520px !important;
//         height: 520px !important;
//     }
// }
@media (min-width: 768px) {
    .container {
        padding-right: 40px;
        padding-left: 40px;
        background: linear-gradient(180deg, #193572 0%, #4D8FEF 100%), #437CD2;
        height: 100%;

        .con {
            display: flex;
            flex-direction: column;
            height: 100%;
            align-items: center;
            font-size: 30px;

            .top {
                width: 100%;
                height: 88px;
                display: flex;
                margin-bottom: 34px;
                font-size: var(--fontSize22);
                align-items: center;

                .left {
                    img {
                        width: 48px;
                        height: 48px;
                    }
                }


            }

            .main {
                margin-top: 68px;
                margin-bottom: 68px;
                margin-left: 58px;
                margin-right: 58px;
                width: 634px;
                height: 634px;
                background: #FFFFFF;
                border-radius: 32px;

                canvas {
                    width: 100% !important;
                    height: 100% !important;
                }
            }
        }
    }
}
</style>