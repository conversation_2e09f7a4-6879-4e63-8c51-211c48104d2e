<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router';
const router = useRouter()
const isSelete = ref(false)
// 定义组件的事件
const emits = defineEmits(['changeData','hideLogin'])
// 改变协议 
const changeAgree = (data: boolean) => {
    if (data) {
        isSelete.value = true
    } else {
        isSelete.value = false
    }
    emits('changeData',data)
}
const gotoDetail = (data:string) => {
    // 跳转到网页隐私政策页面
    router.push({ path: data })
    emits('hideLogin',false) 
}
</script>
<template>
    <div class="agreement">
        <img v-if="isSelete" @click="changeAgree(false)" src="@/assets/images/active/select.png">
        <img v-else @click="changeAgree(true)" src="@/assets/images/active/noselect.png">
        <div class="ment">Read and agreed<span>《<a @click="gotoDetail('/term/privacyPolicy')">Privacy
                    Policy</a>》《<a @click="gotoDetail('/term/termsOfService')">Terms of Service</a>》</span>
        </div>
    </div>
</template>
<style scoped lang="scss">
// @import "@/assets/css/login/index.scss";
.agreement {
        // position: absolute;
        // bottom: -74px;
        margin-top: 34px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        // font-size: 16px;

        img {
            width: 28px;
            height: 28px;
            margin-right: 14px;
        }

        .ment {
            color: #F0F0F0;
            span {
                color: rgba(94, 254, 255, 1);
            }
        }


    }
</style>