import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
// import {pxToViewport} from "./src/plugins/pxto-viewport.ts"
import postcsspxtoviewport from 'postcss-px-to-viewport';
// https://vitejs.dev/config/

// console.log(localStorage.getItem('id'))

export default defineConfig({
  server: {
    open: true,//自动浏览
    host:'0.0.0.0',// 开放本机地址访问
  },
  plugins: [
    vue(),
    vueJsx(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  proxy: {
    '/api': {
      target: 'https://api.twitter.com',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '')
    },
  },
  css: {
    postcss: {
      plugins: [
        postcsspxtoviewport({
          unitToConvert: 'px',
          viewportWidth: 750,
          unitPrecision: 5, // 单位转换后保留的精度
          propList: ['*'], // 能转化为vw的属性列表
          viewportUnit: 'vw', // 希望使用的视口单位
          fontViewportUnit: 'vw', // 字体使用的视口单位
          selectorBlackList: ['ignore-'], // 需要忽略的CSS选择器，不会转为视口单位，使用原有的px等单位。
          minPixelValue: 1, // 设置最小的转换数值，如果为1的话，只有大于1的值会被转换
          mediaQuery: false, // 媒体查询里的单位是否需要转换单位
          // // 只在桌面设备的媒体查询下进行转换
          // mediaQueryRange: {
          //   'mobile': '(max-width: 768px)'
          // },
          replace: true, //  是否直接更换属性值，而不添加备用属性
          exclude: [/^node_modules$/], // 忽略某些文件夹下的文件或特定文件，例如 'node_modules' 下的文件
          // include: [/(\/|\\)(toast)\\.scss$/],
          // /src\/views\/login\//,,/dialog\.css$/ /src\/views\/term\//
          include: [/src\/components\/invitation\//,/src\/views\/invitation\//,/toast\.css$/], 
          // include: [invitation], // 如果设置了include，那将只有匹配到的文件才会被转换
          landscape: false, // 是否添加根据 landscapeWidth 生成的媒体查询条件 @media (orientation: landscape)
          landscapeUnit: 'vw', // 横屏时使用的单位
          landscapeWidth: 1628, // 横屏时使用的视口宽度 
          
        })
      ]
    }       
  }

  // css: {
  //   postcss: {
  //     plugins:[
  //       pxToViewport()
        
  //     ]
     
  //   }
  // }


})
