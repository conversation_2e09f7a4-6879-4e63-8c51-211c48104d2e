import request from '@/service/axios' 
// 登录
export function login(data:any) {
	return request({
		url: '/game-login/web/login',
		method: 'POST',
		data,
	})
}
// 邮箱登录
export function emailLogin(data:any) {
	return request({
		url: '/game-login/web/emailLogin',
		method: 'POST',
		data,
	})
}
// 邮箱登录
// export function emailLogin(data) {
// 	return request({
// 		url: '/game-login/emailLogin',
// 		method: 'POST',
// 		data,
// 	})
// }
// token登录
export function tokenLogin(data:any) {
	return request({
		url: '/game-login/web/tokenLogin',
		method: 'POST',
		data,
	})
}

//注册
export function register(data:any) {
	return request({
		url: '/game-login/emailRegister',
		method: 'POST',
		data,
	})
}
// 获取验证码
export function getCode(data:any) {
	return request({
		url: '/game-login/emailGetCode',
		method: 'POST',
		data,
	})
}
// 忘记密码
export function forgetPassword(data:any) {
	return request({
		url: '/game-login/emailForgetPassword',
		method: 'POST',
		data,
	})
}
//退出
export function quitAccount(data:any) {
	return request({
		url: `/game-login/quitAccount?userId=${data}`,
		method: 'POST',
	})
}
// 注销
export function cancelAccount(data:any) {
	return request({
		url: `/game-login/cancelAccount`,
		method: 'POST',
		data
	})
}
// 绑定
export function binding(data:any) {
	return request({
		url: '/game-login/binding',
		method: 'POST',
		data,
	})
}
// 判断是否已绑定
export function isBinding(data:any) {
	return request({
		url: `/game-login/binding/isBinding?bindingId=${data}`,
		method: 'GET',
		data,
	})
}
// 绑定列表
export function bindingList(data:any) {
	return request({
		url: `/game-login/binding/records`,
		method: 'POST',
        data
	})
}
// googleLogin
export function getGoogleLogin(data:any) {
	return request({
		url: `/game-login/google/login?accessToken=${data}`,
		method: 'POST',
        data
	})
}
// appleLogin
export function getAppleLogin(data:any) {
	return request({
		url: `/game-login/web/apple/login?idToken=${data}`,
		method: 'POST',
        data
	})
}