/* @import './base.css'; */

/* 主题颜色 */
:root {
  --bgColor: linear-gradient(360deg, #2D3968 0%, #212A45 100%);
  --newsBgColor:#F7F7F7FF;
  --activeColor:#86F8FF;
  --fontFamily: SourceHanSansSC, SourceHanSansSC;
  --fontWeight5:500;
  --mainTitleColor:#333333;
  --mainTextColor:rgba(51, 51, 51, 0.6);
  --mainSubtitleColor:rgba(51, 51, 51, 0.9);
  --fontSize32:32px;
  --fontSize30:30px;
  --fontSize28:28px;
  --fontSize22:22px;
  --fontSize20:20px;
  --fontSize18:18px;
  --fontSize16:16px;
  --fontSize14:14px;
  --fontSize12:12px;
}

*{
  padding: 0;
  margin: 0;
}
html,
body {
  /* max-width:1920px; */
  width: 100%;
  height: 100%;
  font-weight: 400;
  font-family: SourceHanSansSC, SourceHanSansSC;
  color: #FFFFFF;
}


#app {
  width: 100%;
  height: 100%;
}






/* #app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  font-weight: normal;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }

  #app {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 0 2rem;
  }
} */