.con {
    margin-left: 64px;
    margin-right: 64px;
    font-size: 16px;
    position: relative;

    .info {
        color: #FF2B2B;
    }

    .prompt {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
        margin-bottom: 10px;

        .forget {
            color: #3E4F75;
        }
    }

    .login {
        color: rgba(1, 1, 1, 0.7);
        background: url('@/assets/images/login/loginBtnBg.png') no-repeat;
        background-size: 100% 100%;
        height: 54px;
        line-height: 54px;
        text-align: center;
    }

    .other {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .other-top {
            margin-top: 9px;
            margin-bottom: 22px;
            display: flex;
            justify-content: center;
            align-items: center;

            .bar {
                width: 104px;
                height: 1px;
                border: 1px solid #899FC6;
            }

            .text {
                margin-left: 10px;
                margin-right: 10px;
                font-size: var(--fontSize14);
                color: rgba(1, 2, 3, 0.74);
                line-height: 20px;
            }
        }

        .other-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 70px;

        }
    }

    .close {
        position: absolute;
        top: -60px;
        right: -36px;

        img {
            width: 34px;
            height: 34px;
        }
    }

    .agreement {
        // position: absolute;
        // bottom: -74px;
        margin-top: 34px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 22px;

        img {
            width: 28px;
            height: 28px;
            margin-right: 14px;
        }

        .ment {
            color: #F0F0F0;

            span {
                color: rgba(94, 254, 255, 1);
            }
        }


    }
}



/* 插槽 */
.verificationCode {
    display: flex;
    // width: 101px;
    align-items: center;

    .split {
        width: 1px;
        height: 30px;
        background: #50648E;
    }

    .code {
        flex: 1;
        text-align: center;
        font-size: 16px;
        line-height: 24px;
        margin-left: 20px;
        color: #475A82;
    }
}