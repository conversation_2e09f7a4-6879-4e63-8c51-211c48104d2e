<script setup lang="ts">
import { getCurrentInstance } from 'vue';
import { useRouter } from 'vue-router';
const router = useRouter()
const instance = getCurrentInstance()
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
const gotoDetail = (url: string) => {
    router.push({ path: url })

}
const logout = () => {
    instance?.proxy?.emitter.emit('mitt-logout', true)
}
</script>

<template>
    <div class="footer">
        <div class="footer-list">
            <div class="footer-left">
                <div class="logo">
                    <img src="@/assets/images/index/logo-footer.png">
                </div>
            </div>
            <div class="footer-right">
                <div>COMPANY：HIGH GENIUS LIMITED</div>
                <div>ADDRESS: A802 Jinyujiahua Building, No. 9 Shangdi 3rd St., Haidian District, Beijing, PRC</div>
                <div>Email: <EMAIL></div>
                <div><a @click="gotoDetail('/term/termsOfService')">Terms of Service</a>
                    <a @click="gotoDetail('/term/privacyPolicy')">Privacy Policy</a>
                    <a @click="gotoDetail('/term/purchaserTerms')">Purchaser Terms</a>
                    <a @click="gotoDetail('/term/customerService')">Customer Service</a>
                    <a @click="gotoDetail('/term/communityGuidelines')">Community Guidelines</a>
                    <!-- <a
                        @click="gotoDetail('/index/currency')">Currency Exchange</a>  -->
                    <a @click="gotoDetail('/login/delete')">Delete Account</a>
                </div>
            </div>
        </div>
    </div>
</template>
<style scoped lang="scss">
.footer {
    width: 100%;
    // height: 287px;
    background-color: #141414;
    /* position: fixed;
    bottom: 0; */

    .footer-list {
        max-width: 1920px;
        margin: auto;
        display: flex;
        padding-top: 105px;

        .footer-left {
            margin: 0 212px 122px 217px;
            display: flex;
            flex-direction: column;
            align-items: center;

            .logo img {
                width: 180px;
                height: 90px;
            }

        }

        .footer-right {
            font-size: var(--fontSize14);
            color: #ECECEC;
            line-height: 33px;
            letter-spacing: 1px;

            a {
                padding-right: 26px;
            }

            a:hover {
                color: var(--activeColor)
            }
        }
    }
}

@import "@/assets/css/media-queries.scss";
</style>