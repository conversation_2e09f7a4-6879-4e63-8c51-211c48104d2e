:deep(.van-toast) {
    background: rgba(0, 0, 0, 0.6);
    width: 80%;
    border-radius: 18px;
  }
  
  .toastCon {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-left: 20px;
    margin-right: 20px;
  
    img {
      margin-top: 46px;
      margin-bottom: 20px;
      width: 80px;
      height: 80px;
    }
  
    .title {
      font-weight: 500;
      font-size: 26px;
      line-height: 30px;
      margin-left: 132px;
      margin-right: 132px;
      margin-bottom: 12px;
    }
  
    .info {
      font-size: 24px;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 36px;
      line-height: 34px;
    }
  }
  :deep .van-cell {
    color: #475A82;
    background: none;
    padding: 0;
    margin-bottom: 10px;
    /* height: 54px; */
}

:deep(.van-field__body) {
    background: rgba(80, 100, 142, 0.3);
    padding: 12px 22px;
    font-size: 22px;
    height: 54px;
}

:deep(.van-field__control::-webkit-input-placeholder) {
    color: #475A82;
    color: rgba(71, 90, 130, 0.6);
}

:deep(.van-field__error-message) {
    font-size: 20px;
    font-weight: 400;
    color: #FF2B2B;
}

:deep(.van-dialog) {
    background: url('@/assets/images/login/bg.png') no-repeat;
    background-size: 100% 100%;
    width: 80%;
    overflow: auto;
}

:deep(.van-dialog__header) {
    text-align: center;
    padding: 26px 0 22px;
    font-size:28px;
    font-weight: 500;
    color: #475A82;
    line-height: 40px;
}
