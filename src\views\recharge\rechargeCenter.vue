<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { useRouter } from 'vue-router';
import yinlin from "@/assets/images/recharge/yinlian.png"
import weixin from "@/assets/images/recharge/weixin.png"
import zhifubao from "@/assets/images/recharge/zhifubao.png"
const router = useRouter()
const dataList = ref(['1', '8', '10', '15'])
const payList = ref([{ id: 0, url: yinlin }, { id: 1, url: weixin }, { id: 2, url: zhifubao }])
const current = ref(0)
const payCurrent = ref(0)
const value = ref('')
const amount = ref('')
const showRecharge = ref(true)

const handleInput = (event) => {
    let str = event;
    current.value = null
    console.log(value)
    if (str > 300) {
        nextTick(() => {
            value.value = 200
            amount.value = 200
        })
    } else {
        amount.value = value
    }
    if (str < 0) {
        nextTick(() => {
            value.value = 1
        })
    } else {

    }
}
const change = (index, item) => {
    current.value = index
    amount.value = item
}
const changePay = (index, item) => {
    payCurrent.value = index
}
</script>

<template>

    <div class="recharge">
        <div class="title">充值中心</div>
        <div class="rechargeCon">
            <div class="rechargeType">
                <div class="title">充值到：</div>
                <div class="amountCon">
                    <div class="selete">
                        <div class="i active">
                            天才币
                            <img class="triangle" src="@/assets/images/recharge/triangle.png" />
                            <img class="tick" src="@/assets/images/recharge/tick.png" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="rechargeType">
                <div class="title">选择金额：</div>
                <div class="amountCon">
                    <div class="selete">
                        <div class="i" :class="[current == index ? 'active' : 'i']" v-for="(item, index) in dataList"
                            @click="change(index, item)">${{ item }}
                            <img v-if="current == index" class="triangle" src="@/assets/images/recharge/triangle.png" />
                            <img v-if="current == index" class="tick" src="@/assets/images/recharge/tick.png" />
                        </div>

                    </div>
                    <el-input placeholder="$ 其它金额" type="number"  v-model="value"
                        @input="handleInput"></el-input>

                </div>
            </div>
            <div class="rechargeType">
                <div class="title">支付方式：</div>
                <div class="amountCon">
                    <div class="selete">
                        <div class="i payi" :class="[payCurrent == index ? 'active' : 'i']"
                            v-for="(item, index) in payList" @click="changePay(index, item)">
                            <img :src="item.url" />
                            <img v-if="payCurrent == index" class="triangle"
                                src="@/assets/images/recharge/triangle.png" />
                            <img v-if="payCurrent == index" class="tick" src="@/assets/images/recharge/tick.png" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="prompt">充值比例1:100，充值成功后，你将获得100天才币</div>
        <div class="confirm">立即充值</div>
    </div>
</template>
<style scoped lang="scss">
.recharge {
    margin: 22px 32px;

    .title {
        font-weight: 500;
        font-size: 20px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 29px;
        margin-bottom: 38px;
    }

    .prompt {
        color: rgba(0, 0, 0, 0.7);
        font-size: 14px;
        text-align: center;
        margin-top: 10px;
        border-bottom: 1px solid #E9E9E9;
        padding-bottom: 18px;
    }

    .confirm {
        width: 110px;
        height: 32px;
        line-height: 32px;
        background: #215FD7;
        border-radius: 2px;
        text-align: center;
        color: #FFFFFF;
        font-size: 14px;
        margin: 30px auto;
    }

    .rechargeCon {

        .rechargeType {
            display: flex;
            // align-items: center;

            .title {
                font-size: 14px;
                color: rgba(0, 0, 0, 0.85);
                width: 70px;
            }

            .amountCon {
                flex: 1;
                margin-left: 10px;

                .selete {
                    padding-bottom: 16px;
                    display: flex;
                    align-content: center;
                    text-align: center;
                    color: #215FD7;
                    font-size: 14px;

                    .i {
                        width: 102px;
                        height: 30px;
                        line-height: 30px;
                        border-radius: 2px;
                        margin-right: 20px;
                        border: 1px solid #CBCBCB;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        img {
                            width: 98px;
                            height: 22px;
                        }
                    }

                    .payi {
                        width: 128px;

                    }

                    .active {
                        border: 1px solid #215FD7;
                        position: relative;

                        .triangle {
                            position: absolute;
                            top: 0;
                            right: 0;
                            width: 24px;
                            height: 24px;
                        }

                        .tick {
                            position: absolute;
                            top: 0;
                            right: 0;
                            width: 14px;
                            height: 14px;
                        }

                    }
                }

                :deep(.el-input) {
                    width:224px;
                    margin-bottom: 33px;
                }

            }


        }

    }
}
</style>