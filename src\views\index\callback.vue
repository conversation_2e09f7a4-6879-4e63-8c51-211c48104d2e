<script setup>
import { onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import axios from 'axios'

const route = useRoute()
const router = useRouter()

onMounted(async () => {
  try {
    const code = route.query.code
    const state = route.query.state
    const error = route.query.error

    // 处理授权错误
    if (error) {
      throw new Error(`Authorization failed: ${error}`)
    }

    // 验证 state
    const storedState = sessionStorage.getItem('twitter_state')
    if (state !== storedState) {
      throw new Error('Invalid state parameter')
    }

    // 获取存储的 code_verifier
    const codeVerifier = sessionStorage.getItem('twitter_code_verifier')

    // 发送授权码到后端
    const response = await axios.post('/api/auth/twitter', {
      code,
      code_verifier: codeVerifier,
      redirect_uri: 'http://localhost:5173/callback'
    })

    // 处理登录成功逻辑
    localStorage.setItem('access_token', response.data.access_token)
    router.push('/')
    
  } catch (error) {
    console.error('Login failed:', error)
    router.push('/login?error=auth_failed')
  } finally {
    // 清理存储
    sessionStorage.removeItem('twitter_code_verifier')
    sessionStorage.removeItem('twitter_state')
  }
})
</script>

<template>
  <div class="callback-container">
    <p>Processing Twitter login...</p>
  </div>
</template>