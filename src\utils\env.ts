const Env = import.meta.env.VITE_APP_TITLE;
console.log(Env)
let configs = {}
if(Env=='production'){
    configs = {
        loginUrl:'https://loginmeta.hapmetasocialltd.com:8010',
        copyUrl:'https://www.hapmetasocialltd.com',
        redirectURI:'https://www.hapmetasocialltd.com/index'
    }
   
}else if(Env=='pre'){
    configs = {
        loginUrl:'https://loginmetapre.hapmetasocialltd.com:6010',
        copyUrl:'https://wwwpre.hapmetasocialltd.com',
        redirectURI:'https://wwwpre.hapmetasocialltd.com/index'
    }
}else if(Env=='test'){
    configs = {
        loginUrl:'https://loginmetatest.hapmetasocialltd.com:6010',
        copyUrl:'https://wwwtest.hapmetasocialltd.com',
        redirectURI:'https://wwwtest.hapmetasocialltd.com/index'
    }
}else{
    configs = {
        loginUrl:'http://192.168.1.109:8010',
        copyUrl:'https://wwwtest.hapmetasocialltd.com',
        redirectURI:' https://bursting-cub-informed.ngrok-free.app/index',
    }
}
export default configs