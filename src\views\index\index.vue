<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'

import TopDownLoad from "@/views/index/topDownLoad.vue"
import News from "@/views/index/news.vue"
import GameIntroduction from '@/views/index/gameIntroduction.vue';
import ProductFeatures from "@/views/index/productFeatures.vue"
// import Footer from '@/views/footer.vue'
// import HeadMenu from '@/views/headMenu.vue'
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
// console.log(countStore.tabActive)
const domID = ref('')
import pingguo from '@/assets/images/index/pingguo 2.png'
import GooglePlay from '@/assets/images/index/a-GooglePlay 2.png'
import anzhuo from '@/assets/images/index/anzhuo 2.png'
// { id: 2, url: anzhuo, title: 'Android Download' ,color:' #4CBDFF'}
const downloadList = [{ id: 0, url: pingguo, title: 'App store Download', color: '#141414' }, { id: 1, url: GooglePlay, title: 'Google play Download', color: '#56C366' }];
const downBtn = ((data:number) => {
  if (data == 0) {
    window.location.href = 'https://apps.apple.com/us/app/hapmeta-social/id6738371086'
  } else if (data == 1) {
    window.location.href = 'https://play.google.com/store/apps/details?id=com.geniusmetaltd.hapmetasocial'
  }
})
// 是否显示下载
const isShowDown = ref(false)
// 监听pinia中的state值
watch(() => countStore.tabActive, (n, o) => {
  console.log(n)
  if (n == 0) {
    domID.value = `#index`
  } else if (n == 1) {
    domID.value = `#news`
  } else if (n == 2) {
    domID.value = `#game`
  } else if (n == 3) {
    domID.value = `#product`
  }
  if (n !== 4) {
    document.querySelector(domID.value)?.scrollIntoView({ behavior: 'smooth', block: 'start' })
    setTimeout(() => {
      countStore.isClick = false
    }, 1000)
  }
})

//滚动时，切换菜单栏
const doScroll = (event: any) => {
  if (countStore.isClick) {

  } else {
    let scrollTop = event.scrollTop + countStore.headMenuDomTop+150;
    console.log(scrollTop)
    if (scrollTop < countStore.newDomTop) {
      console.log('首页')
      countStore.tabActive = 0
    } else if (scrollTop < countStore.gameDomTop) {
      console.log('新闻列表')
      countStore.tabActive = 1
    } else if (scrollTop < countStore.productDomTop-500) {
      // countStore.productDomTop - 150
      console.log('玩法介绍')
      countStore.tabActive = 2
    } else {
      // 2961 2756
      console.log('产品特色')
      countStore.tabActive = 3
    }
  }



}
const index = ref<HTMLElement | null>(null);
const news = ref<HTMLElement | null>(null);
const game = ref<HTMLElement | null>(null);
const product = ref<HTMLElement | null>(null);
onMounted(() => {
  // 获取所有锚点元素的offsetTop
  console.log((index.value as HTMLElement).offsetTop);
  console.log((news.value as HTMLElement).offsetTop);
  console.log((game.value as HTMLElement).offsetTop);
  console.log((product.value as HTMLElement).offsetTop);
  countStore.indexDomTop = (index.value as HTMLElement).offsetTop
  countStore.newDomTop = (news.value as HTMLElement).offsetTop
  countStore.gameDomTop = (game.value as HTMLElement).offsetTop
  countStore.productDomTop = (product.value as HTMLElement).offsetTop
})
// 
const openShow = (data: boolean) => {
  isShowDown.value = data
}
</script>

<template>
  <!-- <div>
    <HeadMenu/>
  </div> -->
  <el-scrollbar ref="testRef" @scroll="doScroll">
    <div id="index" ref="index">
      <TopDownLoad />
    </div>
    <div id="news" ref="news">
      <News />
    </div>
    <div id="game" ref="game">
      <GameIntroduction />
    </div>
    <div id="product" ref="product">
      <ProductFeatures />
    </div>
    <!-- <Footer></Footer> -->
  </el-scrollbar>
  <img class="sudoEnter" src="@/assets/images/index/sudoEnter.png" />
  <div class="upDown">
    <div class="moren" @click="openShow(true)" v-show="!isShowDown">
      <img src="@/assets/images/index/expandBtn.png" alt="">
    </div>
    <div class="show" v-show="isShowDown">
      <div class="upBtn" @click="openShow(false)">
        <img src="@/assets/images/index/upBtn.png" alt="">
      </div>
      <img class="twoCode" src="@/assets/images/index/downCode.png" alt="">
      <div class="download-list">
        <div class="download-item" v-for="item in downloadList" :key="item.id" :style="{ background: item.color }"
          :class="[item.id == 1 ? 'otherMargin' : '']" @click="downBtn(item.id)">
          <!-- <img :src="`/src/assets/images/index/${item.url}`"> -->
          <img :src="item.url">
          <div class="text">{{ item.title }}</div>
        </div>

      </div>
    </div>

  </div>
</template>
<style scoped lang="scss">
.sudoEnter {
  position: fixed;
  right: 32px;
  top: 40%;
  width: 140px;
  height: 220px;
  z-index: 33;
}

.upDown {
  position: fixed;
  right: 0;
  top: 50%;
  z-index: 33;

  .moren {
    width: 30px;
    height: 94px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background: url('@/assets/images/index/upBg.png') no-repeat;
    background-size: 100% 100%;
  }

  .show {
    background: url('@/assets/images/index/sidebarDown.png') no-repeat;
    background-size: 100% 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .upBtn {
      position: absolute;
      width: 30px;
      height: 94px;
      display: flex;
      align-items: center;
      left: -28px;
      top: 35%;
      background: url('@/assets/images/index/upBg.png') no-repeat;
      background-size: 100% 100%;
    }

    .twoCode {
      margin: 24px 24px 0;
      width: 168px;
      height: 168px;
    }

    .download-list {
      margin: 0 20px;

      .download-item {
        height: 58px;
        width: 180px;
        background: #141414;
        border-radius: 8px;
        display: flex;
        align-items: center;
        margin: 18px 0;

        img {
          margin: 0 10px 0 20px;
          width: 34px;
          height: 34px;
        }

        .text {
          font-size: 18px;
          color: #FFFFFF;
          line-height: 24px;
        }
      }

      .otherMargin {
        img {
          margin: 0 7px 0 10px;
        }
      }
    }
  }

}

@import "@/assets/css/media-queries.scss";
</style>
