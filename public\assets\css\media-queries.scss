// _media-queries.scss文件中的媒体查询封装
@mixin media($query) {
    @if $query =="mobile" {

        // 手机屏幕
        @media (max-width: 768px) {
            @content;
        }
    }

    @else if $query =="tablet" {

        // 平板电脑屏幕 (min-width: 769px) and (max-width: 1024px)
        @media (min-width: 769px) and (max-width: 1200px) {
            @content;
        }
    }

    @else if $query =="desktop" {

        // 台式电脑屏幕min-width: 1025px
        @media (min-width: 1200px) {
            @content;
        }
    }
}

// 当视口宽度大于 600 像素时，应用样式规则。

@media (min-width: 760px) {
    /* 样式规则 */
}

// 当视口宽度小于等于 600 像素时，应用样式规则。

@media (max-width: 760px) {
    /* 样式规则 */
}

// 当视口宽度在 600 到 1000 像素之间时，应用样式规则。

@media (min-width: 760px) and (max-width: 1200px) {
    /* 样式规则 */
}


// 在需要使用媒体查询的地方调用该封装

@include media("mobile") {

    // 导航栏
    .header {
        font-size: 10px;

        .header-top .logo {
            margin-left: 0;
            font-size: 10px;

            img {
                width: 93px;
                height: 20px;
                display: none;
            }
        }


        .header-top .header-menu li {
            width: 70px;
        }
    }

    // 首页下载
    .container {

        .download {
            width: 250px;
            height: 67px;

            .twoCode {
                width: 48px;
                height: 48px;
                margin: 0 5px 0 26px;
            }

            .download-list {
                margin: 8px 0;
                gap: 5px;

                .download-item {
                    width: 70px;
                    height: 22px;
                    border-radius: 3px;

                    img {
                        margin: 0 3px 0 7px;
                        width: 13px;
                        height: 13px;
                    }

                    .text {
                        font-size: 8px;
                        line-height: 9px;
                    }
                }

                .otherMargin {
                    img {
                        margin: 0 3px 0 5px;
                    }
                }
            }
        }
    }

    // title
    .newContainer {
        height: 800px;

        .title {
            margin-top: 40px !important;
            // width: 530px;
            height: 100px !important;
        }
    }

    // .newContainer {
    //     height: 1020px;
    // }


    // 首页下载背景
    .topDownLoadBg {
        height: 400px;
    }

    // 首页
    .sudoEnter {
        width: 70px;
        height: 110px
    }

    .upDown {
        .moren {
            width: 20px;
            height: 64px;

            img {
                width: 12px;
                height: 12px;
            }
        }

        .show {
            .upBtn {
                width: 20px;
                height: 64px;
                left: -20px;
                img {
                    width: 12px;
                    height: 12px;
                }
            }

            .twoCode {
                margin: 12px 10px 0;
                width: 90px;
                height: 90px;

            }

            .download-list {
                margin: 0 10px;

                .download-item {
                    height: 26px;
                    width: 90px;
                    margin: 9px 0;
                    img {
                        width: 17px;
                        height: 17px;
                        margin: 0 5px 0 10px;
                    }

                    .text {
                        font-size: 10px;
                        line-height: 10px;
                    }
                }

            }
        }
    }

    // news game
    .newsList-item {
        font-size: 7px !important;
    }

    .con-right-top,
    .introduction,
    .con-top {
        font-size: 4px !important;
        margin: 0 !important;
        // .con-top-item {
        //     width: 120px !important;
        // }
    }

    .introduction,
    .con-show {

        // width: 710px !important;
        img {
            height: 378px !important;
        }
    }

    .conWeb {
        flex-direction: column;
        align-items: center;

        .con-left,
        .con-right {
            flex: none;
            width: 100%;
            height: 300px !important;
            margin-right: 0 !important;
            margin-bottom: 30px;
        }

        .con-right-top {
            width: 100%;
        }

    }


    // 底部
    .footer .footer-list {
        padding-top: 29px;

        .footer-left {
            margin: 0 62px 37px 64px;

            .logo img {
                width: 70px;
                height: 35px;
            }
        }

        .footer-right {
            font-size: 5px;
        }
    }

    // 货币兑换
    .container {

        .con-currency {
            width: 468px;
            height: 275px;
            margin-top: 41px;

            .titles {
                height: 27px;
                margin: 17px 0 21px;
            }

            .exchangeWay {
                width: 189px;
                height: 41px;

                .mask {
                    img {
                        width: 26px;
                        height: 26px;
                        margin: 0 7px 0 5px;
                    }
                }

            }

            .quantity {
                width: 189px;
                margin-top: 40px;

                img {
                    width: 18px;
                    height: 18px;
                    margin: 0 5px;
                }
            }

            .obtain {
                margin: 15px 0 6px;

                .info {
                    font-size: 5px;
                    line-height: 8px;
                }

                img {
                    width: 12px;
                    height: 12px;
                    margin: 0 1px 0 2px;
                }

                .result {
                    font-size: 6px;
                    line-height: 9px;
                }
            }

            .btn {
                width: 112px;
                height: 38px;
                line-height: 38px;
                font-size: 12px
            }

        }

        :deep(.el-input__inner) {
            height: 16px;
            font-size: 5px;
        }

        .footer {
            margin-top: 20px;
            font-size: 6px;
            line-height: 12px;
        }
    }

    // 注销   确认注销
    .con {
        font-size: 6px;

        .top {
            margin-bottom: 8px;
            font-size: 9px;
        }

        .info {
            margin-bottom: 8px;
        }

        .end {
            margin-bottom: 48px;
        }

        .btn {
            margin: 23px 0 14px;
        }

        :deep(.el-button) {
            height: 18px;
            font-size: 6px;
        }
    }

    // 活动页
    .container {

        .headMenu {
            .top {
                font-size: 18px;
                margin-left: 26px;
                margin-right: 26px;

                img {
                    width: 150px;
                    height: 40px;
                }
            }

            .bgText {
                font-size: 34px;
            }
        }

        .bottomCon {
            .centerBtn {
                padding: 74px 40px 36px;
                font-size: 10px;
                height: 68px;
                line-height: 68px;
            }

            .title {
                width: 100% !important;
                height: 42px !important;
            }

            .rule {
                line-height: 20px;
            }
        }
    }


}

@include media("tablet") {

    // padding: 15px;
    .header {
        font-size: var(--fontSize16);

        .header-top .logo img {
            width: 160px;
            height: 40px;
        }

        .header-top .header-menu li {
            width: 100px;
        }
    }

    // 首页下载
    .container {
        .download {
            width: 470px;
            height: 130px;

            .twoCode {
                width: 60px;
                height: 60px;
                margin: 0 10px 0 54px;
            }

            .download-list {
                margin: 16px 0;
                gap: 10px;

                .download-item {
                    width: 153px;
                    height: 48px;
                    border-radius: 6px;

                    img {
                        margin: 0 6px 0 14px;
                        width: 26px;
                        height: 26px;
                    }

                    .text {
                        font-size: 16px;
                        line-height: 16px;
                    }
                }

                .otherMargin {
                    img {
                        margin: 0 6px 0 10px;
                    }
                }
            }
        }
    }

    // 首页下载背景
    .topDownLoadBg {
        height: 600px;
    }

    // news game
    .newsList-item {
        font-size: 10px !important;
    }

    .con-right-top,
    .introduction,
    .con-top {
        font-size: 10px !important;

        // .con-top-item {
        //     width: 190px !important;
        // }
    }

    // 底部
    .footer .footer-list {
        padding-top: 50px;

        .footer-left {
            margin: 0 122px 95px 168px;

            .logo img {
                width: 140px;
                height: 70px;
            }
        }

        .footer-right {
            font-size: 10px;
        }
    }

    // 货币兑换
    .container {

        .con-currency {
            width: 900px;
            height: 500px;
            margin-top: 82px;

            .titles {
                height: 45px;
                margin: 32px 0 45px;
            }

            .exchangeWay {
                width: 400px;
                height: 82px;

                .mask {
                    img {
                        width: 51px;
                        height: 51px;
                        margin: 0 14px 0 10px;
                    }
                }

            }

            .quantity {
                width: 400px;
                margin-top: 80px;

                img {
                    width: 40px;
                    height: 40px;
                    margin: 0 10px;
                }
            }

            .obtain {
                margin: 30px 0 12px;

                .info {
                    font-size: 10px;
                    line-height: 16px;
                }

                img {
                    width: 24px;
                    height: 24px;
                    margin: 0 2px 0 4px;
                }

                .result {
                    font-size: 12px;
                    line-height: 18px;
                }
            }

            .btn {
                width: 224px;
                height: 72px;
                line-height: 72px;
                font-size: 24px
            }

        }

        :deep(.el-input__inner) {
            height: 38px;
            font-size: 12px;
        }

        .footer {
            margin-top: 40px;
            font-size: 12px;
            line-height: 24px;
        }
    }

    // 活动页
    .container .headMenu {
        // height: 60% !important;
    }
}

@include media("desktop") {

    // 活动页
    .headMenu {
        // height: 80% !important;
    }
}