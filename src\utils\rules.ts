import type { FieldRule } from 'vant'
const mobileRules = [
  { required: true, message: 'Please enter a phone number' },
  { pattern: /^1[3-9]\d{9}$/, message: 'The phone number is incorrect' }
]
const passwordRules= [
  { required: true, message: 'Please enter your password' },
  { pattern:  /^(?![\\d]+$)(?![a-zA-Z]+$)(?![^\\da-zA-Z]+$).{6,20}$/, message: '6-20 characters must include letters and numbers (cannot contain spaces)' }
]
 const emailRules= [
    { required: true, message: 'Please enter your email ' },
    { pattern: /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/, message: 'Please enter a valid email address' }
  ]
  const codeRules= [
    { required: true, message: 'Please enter the verification code' },
    { pattern:/^\d{6}$/, message: 'Length is 6 characters' }
  ]
export { mobileRules, passwordRules,emailRules,codeRules }
