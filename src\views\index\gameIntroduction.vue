<script setup lang="ts">
import { ref } from 'vue'
const introductionList = [{ id: 0, title: 'Character Customization' }, { id: 1, title: 'Friends reward' }, { id: 2, title: 'Mutiple Scenes' }, { id: 3, title: 'Social Interaction' }]
const current = ref(0)
const changeIntroduction = (i: number) => {
    // console.log(i)
    current.value = i
}
</script>

<template>
    <div class="container newContainer">
        <div class="title"></div>
        <div class="con">
            <div class="con-top">
                <div class="con-top-item" :class="[current == item.id ? 'activeList' : '']"
                    v-for="item in introductionList" :key="item.id" @click="changeIntroduction(item.id)">{{ item.title
                    }}</div>

            </div>
            <div class="con-show">
                <img v-if="current == 0" src="@/assets/images/index/game01.png" />
                <img v-if="current == 1" src="@/assets/images/index/game02.png" />
                <img v-if="current == 2" src="@/assets/images/index/game03.png" />
                <img v-if="current == 3" src="@/assets/images/index/game04.png" />
                <!-- <div class="introduction">Character DIY, one of a kind</div> -->
            </div>
        </div>
    </div>
</template>
<style scoped lang="scss">
@import '@/assets/css/index/indexBg.scss';


.container {
    background: url('@/assets/images/index/gameIntroductionBg.png') no-repeat;
    background-size: 100% 100%;

    .title {
        background: url('@/assets/images/index/gameIntroduction.png') no-repeat;
        background-size: 100% 100%;
        /* width: 868px; */
        width: 40%;
    }

    .con {
        margin-top: 24px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .con-top {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 0;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 500;
            line-height: 29px;
            letter-spacing: 1px;
            height: 66px;
            margin: 0 70px;

            .con-top-item {
                height: 54px;
                width: 100%;
                /* width: 275px; */
                /* width: 158px; */
                line-height: 54px;
                text-align: center;
                background: url('@/assets/images/index/btnNoSelect.png') no-repeat;
                background-size: 100% 100%;
                overflow: hidden;
                /* 确保内容超出容器时会被隐藏 */
                white-space: nowrap;
                /* 确保文本在一行内显示，避免换行 */

            }

            .activeList {
                background: url('@/assets/images/index/btnSelect.png') no-repeat;
                background-size: 100% 100%;
            }
        }

        .con-show {
            flex: 1;
            margin-top: 28px;
            /* width: 1200px; */
            width: 100%;
            display: flex;
            justify-content: center;

            img {
                max-width: 100%;
                height: 470px;
            }

            .introduction {
                margin-top: -6px;
                text-align: center;
                height: 74px;
                line-height: 74px;
                background: url('@/assets/images/index/introductionBg.png') no-repeat;
                background-size: 100% 100%;
                font-size: 18px;
                letter-spacing: 1px;
            }
        }
    }

}

@import "@/assets/css/media-queries.scss";
</style>
