<script setup lang="ts">
import { reactive, ref, defineEmits, onMounted } from 'vue'
import { getCode, register } from "@/api/index"
import { emailRules, passwordRules, codeRules } from "@/utils/rules.ts"
import { showLoadingToast, showToast, showFailToast } from 'vant';
import { useRouter } from 'vue-router';
const router = useRouter()
import debounce from 'lodash/debounce';
import axios from 'axios';
import Agreement from "@/components/agreement.vue"
const isSelete = ref(false)
const loginFormRef = ref();
const props = defineProps({
    dialogVisibleRegister: {
        type: Boolean,
        require: false,
        default: false
    }

})

onMounted(() => {
    console.log('挂载完毕')
    // isSeleteUrl.value=isSeleteAgree.noselete
})
// 定义组件的事件
const emits = defineEmits(['dialogClose', 'dialogCloseRegister', 'dialogLogin'])
const handleClose = () => {
    // 触发自定义事件来通知父组件关闭弹窗
    emits('dialogCloseRegister', false)
    userInfo.email = ''
    userInfo.password = ''
    userInfo.code = ''
}
const name = ref('')
const userInfo = reactive({
    email: '',
    password: '',
    code: ''
})
// 获取验证码
let intervalId;
const showCode = ref(true)
let num = ref(0)
const getVerificationCode = debounce(() => {
    console.log('防抖触发点击事件');
    showCode.value = false
    code()
    // 设置60秒倒计时
    const count = 60;
    num.value = count;
}, 300);
const code = async () => {
    // 发送获取验证码的请求逻辑
    // 这里只是示例，你需要根据实际API来编写
    // 假设API返回成功表示可以发送验证码
    try {
        const query = {
            email: userInfo.email
        }
        const res = await getCode(query);
        console.log(res)
        if (res.code == 3) {
            showToast({
                message: 'Please check if the email format is correct',
            });
        } else if (res.code == 2) {
            showToast({
                message: 'The email cannot be empty',
            });
        } else if (res.code == 0) {
            intervalId = setInterval(() => {
                if (num.value <= 0) {
                    clearInterval(intervalId);
                    showCode.value = true
                } else {
                    num.value--;
                }
            }, 1000);
        } else if (res.code == 12) {
            showCode.value = true
            showToast('Email verification code sending failed')
        } else {
            showCode.value = true
            showFailToast('Failed to get the verification code')
        }
    } catch (error) {
        showFailToast('Failed to get the verification code')
    }
};
// 点击去登录
const login = () => {
    emits('dialogLogin', true)
    emits('dialogCloseRegister', false)

}
const changeData = (data: boolean) => {
    isSelete.value = data
}
// 表单验证
const onSubmit = (values) => {
    loginFormRef.value?.validate().then(() => {
        // 验证通过
        registerBtn()
    }).catch((err) => {
        //验证失败
        showFailToast('Please fill in the information correctly');
    })
};
//注册
const registerBtn = async () => {  // 表单输入完毕后点登录调用handleFinish函数
    try {
        if (isSelete.value) {
            const query = {
                email: userInfo.email,
                passWord: userInfo.password,
                code: userInfo.code
            }
            console.log(query)
            const res = await register(query);
            console.log(res)
            if (res.code == 6) {
                // 注册成功
                showToast({
                    message: 'Registration successful',
                });
                emits('dialogCloseRegister', false)
                emits('dialogLogin', true)
            } else if (res.code == 5) {
                // 注册失败
                // 密码格式不正确
                showToast({
                    message: 'The password format is incorrect',
                });
            } else if (res.code == 8) {
                showToast({
                    message: 'The current account already exists',
                });
            } else {
                showFailToast('Registration failed')
            }
        } else {
            showToast({
                message: 'Please check the agreement first',
            });
        }
    } catch (error) {
        console.error('Fetch error:', error);
    }
}
</script>

<template>
    <div>
        <van-dialog v-model:show="props.dialogVisibleRegister" title="Register" align-center :show-confirm-button="false">
            <div class="con">
                <div class="close" @click="handleClose">
                    <img src="@/assets/images/active/close.png" alt="">
                </div>
                <van-form ref="loginFormRef">
                    <van-field v-model="userInfo.email" placeholder="Please enter your email " :rules="emailRules" />
                    <van-field v-model="userInfo.code" placeholder="Please enter the verification code" :rules="codeRules">
                        <template #button>
                            <div class="verificationCode">
                                <div class="split"></div>
                                <div class="code" @click="getVerificationCode" v-if="showCode">Get verification code</div>
                                <div class="code" v-else>{{ num }}</div>
                            </div>
                        </template>
                    </van-field>
                    <van-field v-model="userInfo.password" placeholder="Please enter your password" type="password" show-password
                        :rules="passwordRules" />
                </van-form>
                <!-- <div class="info">请输入正确的密码</div> -->
                <div class="login" @click="onSubmit">Register</div>
                <div class="prompt">
                    <div class="forget" @click="login">Log in</div>
                </div>
                <Agreement @changeData="changeData"></Agreement>
            </div>
        </van-dialog>
    </div>
</template>
<style scoped lang="scss">
@import "@/assets/css/login/dialog.css";
@import "@/assets/css/login/index.scss";
</style>
