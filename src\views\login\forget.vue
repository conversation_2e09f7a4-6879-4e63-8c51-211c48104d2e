<script setup lang="ts">
import { reactive, ref, defineComponent, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { getCode, forgetPassword } from "@/api/index"
import { emailRules, passwordRules, codeRules } from "@/utils/rules"
import { showLoadingToast, showToast, showFailToast } from 'vant';
const loginFormRef = ref<FormInstance>()
const props = defineProps({
    dialogVisibleForget: {
        type: Boolean,
        require: false,
        default: false
    }

})
// 定义组件的事件
const emits = defineEmits(['dialogCloseForget'])
const handleClose = () => {
    // 触发自定义事件来通知父组件关闭弹窗
    emits('dialogCloseForget', false)
    userInfo.email = ''
    userInfo.password = ''
    userInfo.code = ''
}
const name = ref('')
const userInfo = reactive({
    email: '',
    password: '',
    code: ''
})
const rules = reactive<FormRules>({
    password: passwordRules,
    email: emailRules,
})

// 获取验证码
let intervalId: number | null = null;
const showCode = ref(true)
let num = ref(0)
const getVerificationCode = async () => {
    showCode.value = false
    // 发送获取验证码的请求逻辑
    // 这里只是示例，你需要根据实际API来编写
    // 假设API返回成功表示可以发送验证码
    try {
        const query = {
            email: userInfo.email
        }
        const res = await getCode(query);
        console.log(res)
        if (res.code == 3) {
            showCode.value = true
            ElMessage({
                message: 'Please check if the email format is correct',
            });
        } else if (res.code == 2) {
            showCode.value = true
            ElMessage({
                message: 'The email cannot be empty',
            });
        } else if (res.code == 0) {

            // 设置60秒倒计时
            const count = 60;
            num.value = count;
            intervalId = setInterval(() => {
                if (num.value <= 0) {
                    clearInterval(intervalId);
                    showCode.value = true
                } else {
                    num.value--;
                }
            }, 1000);
        } else if (res.code == 12) {
            showCode.value = true
            ElMessage({
                message: 'Email verification code sending failed ',
            });
        } else {
            ElMessage.error({
                message: 'Failed to get the verification code',
            });
        }


    } catch (error) {
        showCode.value = true
        ElMessage.error({
            message: 'Failed to get the verification code',
        });
    }
};
// 表单验证
const onSubmit = (values) => {
    loginFormRef.value?.validate().then(() => {
        // 验证通过
        forgetPasswordBtn()
    }).catch((err) => {
        //验证失败
        ElMessage('Please fill in the information correctly');
    })
};
// 忘记密码 确认修改
const forgetPasswordBtn = async () => {  // 表单输入完毕后点登录调用handleFinish函数
    try {
        var query = {
            email: userInfo.email,
            passWord: userInfo.password,
            code: userInfo.code
        }
        console.log(query)
        const res = await forgetPassword(query);
        console.log(res)
        if (res.code == 0) {
            // 修改成功
            ElMessage.success('Modification successful');
            emits('dialogCloseForget', false)
        } else {
            // 修改失败
            ElMessage.error(res.msg)
        }

    } catch (error) {
        console.error('Fetch error:', error);
    }
}
</script>

<template>
    <div>
        <el-dialog class="loginDialog" :model-value="props.dialogVisibleForget" title="Forgot password" width="30%"
            align-center @close="handleClose">
            <div class="con">
                <el-form ref="loginFormRef" :rules="rules" :model="userInfo">
                    <el-form-item prop="email">
                        <el-input v-model="userInfo.email" placeholder="Please enter your email address" />
                    </el-form-item>
                    <el-form-item>
                        <el-input v-model="userInfo.code" placeholder="Please enter you  Verification Code"
                            :rules="codeRules">
                            <template #suffix>
                                <div class="split"></div>
                                <div class="code" @click="getVerificationCode" v-if="showCode">Get verification code</div>
                                <div class="code" v-else>{{ num }}</div>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item prop="password">
                        <el-input v-model="userInfo.password" placeholder="Please enter your password" show-password
                            :rules="passwordRules" />
                    </el-form-item>
                    <div class="login" @click="onSubmit">confirm</div>
                </el-form>
            </div>

        </el-dialog>
    </div>
</template>

<style scoped>
@import "@/assets/css/login/dialog.css";

.con {
    margin: 0 64px;
    font-size: 16px;

    .login {
        color: rgba(1, 1, 1, 0.7);
        background: url('@/assets/images/login/loginBtnBg.png') no-repeat;
        background-size: 100% 100%;
        height: 54px;
        line-height: 54px;
        text-align: center;
    }

}


/* 插槽 */
.split {
    width: 1px;
    height: 30px;
    background: #50648E;
}

.code {
    font-size: 16px;
    line-height: 24px;
    margin-left: 20px;
}
</style>
