import { createRouter, createWebHistory } from 'vue-router'
import Layout from "@/components/layout.vue";
const routes = [
  {
    path: '/',
    redirect: '/index',
    name: "layout",
    component: Layout,
    children:[
      {
        path: '/index',
        name: 'index',
        component: () => import('@/views/index/index.vue')
      },
      {
        path: '/callback',
        name: 'callback',
        component: () => import('@/views/index/callback.vue')
      },
      {
        path: '/index/currency',
        name: 'index/currency',
        component: () => import('@/views/index/currency.vue')
      },
      {
        path: '/newsDynamic',
        name: 'newsDynamic',
        component: () => import('@/views/newsDynamic/index.vue')
      }, {
        path: '/newsDynamic/detail',
        name: 'newsDynamic-detail',
        component: () => import('@/views/newsDynamic/detail.vue')
      },{
        path: '/term/privacyPolicy',
        name: 'term/privacyPolicy',
        component: () => import('@/views/term/privacyPolicy.vue')
      },
      {
        path: '/term/termsOfService',
        name: 'term/termsOfService',
        component: () => import('@/views/term/termsOfService.vue')
      },
      {
        path: '/term/purchaserTerms',
        name: 'term/purchaserTerms',
        component: () => import('@/views/term/purchaserTerms.vue')
      },
      {
        path: '/term/communityGuidelines',
        name: 'term/communityGuidelines',
        component: () => import('@/views/term/communityGuidelines.vue')
      },
      {
        path: '/term/customerService',
        name: 'term/customerService',
        component: () => import('@/views/term/customerService.vue')
      },
      {
        path: '/login/delete',
        name: 'login/delete',
        component: () => import('@/views/login/logout.vue')
      },
      {
        path: '/recharge',
        name: 'recharge',
        component: () => import('@/views/recharge/index.vue'),
        redirect: '/recharge/rechargeCenter',
        children:[
          {
            path: '/recharge/rechargeCenter',
            name: 'rechargeCenter',
            component: () => import('@/views/recharge/rechargeCenter.vue')
          },
          {
            path: '/recharge/rechargeRecord',
            name: 'rechargeRecord',
            component: () => import('@/views/recharge/rechargeRecord.vue')
          },
        ]
      }

    ]
  },
  {
    path: '/invitation',
    name: 'invitation',
    component: () => import('@/views/invitation/index.vue')
  }, {
    path: '/invitation/code',
    name: 'invitation/code',
    component: () => import('@/views/invitation/code.vue')
  }

]
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

export default router
