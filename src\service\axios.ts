
import axios from 'axios' 
import config  from '@/utils/env'
//服务器请求地址
// http://192.168.1.109:8010
// http://123.57.24.57:8010
// https://8010.supergenius.cn 内网

// 创建axios实例
const request = axios.create({
    // 这里可以放一下公用属性等。
    baseURL: config.loginUrl,
      headers: {
        // 'Authorization' : 'Bearer '+localStorage.getItem('token'),
        // X-Litemall-Token这个名称就固定这个 localStorage.getItem("token")
        // 'Authorization' : 'Bearer eyJ0eXAiOiJhY2Nlc3MiLCJhbGciOiJIUzUxMiJ9.eyJvaWQiOjc4MCwidWlkIjoiMjhlZmU1ODY2ODcwMmNhOGVjZWY3OTBkYmMxZWVkN2IiLCJ1c2VyU24iOiJTRzIwMjQwMjAxNTY1OTA3NSIsImV4cCI6MTcxODA4NTk3Nn0.BtGQuXVMx_-pNOruz2iNIkgIbiRLExV9jfRH20ih5iGxo2lNL6uj99Jl_vvSWbatknHbzuNIDDtij-03FkY8Hg'
      }
})
 
// 请求拦截器
request.interceptors.request.use((config) => {
//token名称以自己的为定，我的是‘satoken’，如果不需要if这里就可以直接删掉
// if (localStorage.getItem('token')) {
//     config.headers['Authorization'] ='Bearer '+localStorage.getItem('token'); //携带token
//     // config.headers['Content-type'] = 'application/json';
// }
    return config;
}, (error) => {
    return Promise.reject(error)
})
 
request.interceptors.response.use((response) => {
//返回码以自己的为定，如果没有安装elementui就换成自己的提示
    let { code, msg } = response.data
    // if (code != 200) {
    //     Message({ message: msg || 'error', type: 'warning' })
    // }
    return response.data;//此处可以只返回后端传出的数据（第一层data是axios封装的）
}, (error) => {
    
    // Message({ message:error.response?.data?.message||'服务器错误', type: 'error' })
   
    // Message.error(err.response?.data?.message||'服务器错误'); 
    return Promise.reject(error)
})
 
export default request;//记得暴露出去
