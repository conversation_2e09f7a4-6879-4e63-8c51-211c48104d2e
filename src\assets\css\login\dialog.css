/* el-dialog样式修改 */
:deep(.el-dialog) {
    background: url('@/assets/images/login/bg.png') no-repeat;
    background-size: 100% 100%;
    width: 552px;
    /* height: 426px; */
    height: 527px;
}

:deep(.el-dialog__header) {
    text-align: center;
    padding: 26px 0 22px;

    .el-dialog__title {
        font-size: var(--fontSize28);
        font-weight: var(--fontWeight5);
        color: #475A82;
        line-height: 40px;
    }

}

/* el-input样式修改 */
:deep(.el-input) {
    /* width: 384px; */
    margin-bottom: 10px;
}

:deep(.el-input__inner) {

    font-size: 16px;
    font-family: SourceHanSansSC, SourceHanSansSC;
    font-weight: 400;
    color: #475A82;
    height: 46px;
}

:deep(.el-input__wrapper) {
    box-shadow: none;
    background: rgba(80, 100, 142, 0.3);
    border: 2px solid rgba(39, 60, 109, 0.3);
}

:deep(.el-input__wrapper:hover) {
    box-shadow: none;
    border: 2px solid #8FFAFF;
}

:deep(.el-input__wrapper.is-focus) {
    box-shadow: none;
    border: 2px solid #8FFAFF;
}

:deep(.el-input__prefix) {
    color: rgba(71, 90, 130, 0.6);
}

:deep(.el-input__inner::-webkit-input-placeholder) {
    color: rgba(71, 90, 130, 0.6);
}

/* 插槽 */
:deep(.el-input__suffix) {
    color: #50648E;
}

:deep(.el-form-item.is-error .el-input__wrapper) {
    box-shadow: none;
}