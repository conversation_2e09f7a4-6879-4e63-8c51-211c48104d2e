<script setup lang="ts">
import { ref } from 'vue'

</script>

<template>
    <div class="container">
        <div class="banner">
            <img src="@/assets/images/index/banner (1).png" />
            <div class="con">
                <div class="con-detail">
                    <div class="con-detail-title">网易云音乐学生会员降价至每月5元 丁磊称用最大力度回馈年轻人</div>
                    <div class="con-detail-time">时间：2021.12.12</div>
                    <div class="con-detail-text">
                        面向年轻音乐音乐爱好者特惠，
                        “网易云音乐学生会员”定价每月5元，畅享高品质音乐服务。网易云音乐学生会员所享权益涵盖平台二十余项专属权益，可以为年轻用户提供更优质的音乐体验，包括海量曲库付费歌曲下载、无损音质畅享等；更多精选付费内容和推荐，包括编辑精选、个性化推荐内容等；专属会员装扮权益，包括头像挂件、播放器模式、主题皮肤等；丰富强大的线上线下权益，包括商城折扣、专属电台，线下演出、电影票务特权及各类生活福利等。此次推出的网易云音乐学生会员，将更优质的音乐及服务体验的参与门槛下调，让14-22周岁年轻人用更优惠的价格享受到更多精品内容与服务。

                        　 <div>
                            　目前，网易云音乐月活规模达2.07亿人，超9成用户为90后00后。作为高品质音乐服务的象征，网易云音乐还将不断优化黑胶会员体系的品牌体验和权益感知，持续提升VIP服务体验感。平台也将继续为会员群体推出更多精品化的内容与专属服务，为音乐爱好者不断打造品位、优质音乐服务。
                        </div>
                    </div>

                </div>


            </div>
        </div>
    </div>
</template>
<style scoped lang="scss">
.container {
    background-color: var(--newsBgColor);
    height: 100%;
    overflow: auto;

    .banner {
        img {
            width: 100%;
        }
    }

    .con {
        width: 70%;
        background-color: #fff;
        margin: 0 auto;

        .con-detail {
            padding: 0 50px;

            .con-detail-title {
                padding: 51px 0 14px;
                font-size: var(--fontSize32);
                font-weight: var(--fontWeight5);
                color: var(--mainTitleColor);
                line-height: 46px;
                letter-spacing: 2px;
                text-align: center;
            }

            .con-detail-time {
                text-align: center;
                font-size: var(--fontSize16);
                color: rgba(51, 51, 51, 0.6);
                line-height: 24px;
                letter-spacing: 1px;
                margin-bottom: 47px;
            }

            .con-detail-text {
                font-size: var(--fontSize18);
                color: rgba(51, 51, 51, 0.6);
                line-height: 34px;
                text-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.5);
                border-top: 1px solid #D9D9D9;
                padding-top: 19px;
            }
        }
    }


}

@media (max-width: 768px) {
    .container {
        .con {
            width: 85%;
            .con-detail {
                padding: 0 20px;

                .con-detail-title {
                    padding: 25px 0 7px;
                    font-size: 20px;
                    line-height: 28px;
                }

                .con-detail-time {
                    margin-bottom: 22px;
                }

                .con-detail-text {
                    font-size: 16px;
                    color: rgba(51, 51, 51, 0.6);
                    line-height: 30px;
                    text-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.5);
                    border-top: 1px solid #D9D9D9;
                    padding-top: 19px;
                }
            }
        }


    }

}
</style>
