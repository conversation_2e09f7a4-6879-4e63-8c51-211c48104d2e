import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

// export const useCounterStore = defineStore('counter', () => {

//   const tabActive = ref(0)
//   const tabListRef = ref([])
//   const count = ref(0)
//   const doubleCount = computed(() => count.value * 2)
//   function increment() {
//     count.value++
//   }

//   return { count, doubleCount, increment }
// })
 const useCounterStore = defineStore('counter',{
  state:()=>({
    isClick:false,
    tabActive:0,
    headMenuDomTop:0,
    indexDomTop:0,
    newDomTop:0,
    gameDomTop:0,
    productDomTop:0,
    login:ref(localStorage.getItem('name')),
    // 别人打开链接传入的id
    id:''
  })
 })
 export default useCounterStore