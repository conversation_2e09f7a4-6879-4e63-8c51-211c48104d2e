<template>
    <div class="container newContainer">
        <div class="title"></div>
        <div class="con">
            <el-carousel :interval="4000" type="card">
                <el-carousel-item v-for="item in swiperList" :key="item">
                    <!-- <h3 text="2xl" justify="center">{{ item }}</h3> -->
                    <!-- <img :src="`/src/assets/images/index/${item.url}`" /> -->
                    <img class='carouselImg' :src="item.url">
                </el-carousel-item>
            </el-carousel>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import game1 from '@/assets/images/index/swiper01.png'
import game2 from '@/assets/images/index/swiper02.png'
import game3 from '@/assets/images/index/swiper03.png'
import game4 from '@/assets/images/index/swiper04.png'
import game5 from '@/assets/images/index/swiper05.png'
const swiperList = [{ id: 1, url: game1 }, { id: 2, url: game2 }, { id: 3, url: game3 }, { id: 4, url: game4 }, { id: 5, url: game5 }]
</script>

<style scoped lang="scss">
@import '@/assets/css/index/indexBg.scss';

.el-carousel {
    width: 100%;
}

.el-carousel__item h3 {
    color: #475669;
    opacity: 0.75;
    line-height: 520px;
    margin: 0;
    text-align: center;
}

// .el-carousel__item:nth-child(2n) {
//     background-color: #99a9bf;
// }

// .el-carousel__item:nth-child(2n + 1) {
//     background-color: #d3dce6;
// }
:deep(.el-carousel__mask) {
    background: none;
}

:deep(.el-carousel__container) {
    height: 520px;
}

.container {
    .title {
        background: url('@/assets/images/index/productFeatures.png') no-repeat;
        /* width: 868px; */
        background-size: 100% 100%;
        width: 40%;
    }

    .con {
        padding-top: 44px;
        display: flex;
        width: 90%;
        flex: 1;
        margin: 0 auto;

        .carouselImg {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }
}

@import "@/assets/css/media-queries.scss";
</style>
