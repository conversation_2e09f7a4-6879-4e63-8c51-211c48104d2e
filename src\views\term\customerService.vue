<script setup lang="ts">
import { ref } from 'vue'

</script>

<template>
    <div class="container">

        <div class="con">
            <div class="con-detail">
                <div class="con-detail-text-subtitle">
                    Dear Valued User,<br><br>

                    We are committed to providing you with a superior software experience. Should you encounter any
                    issues or have any feedback while using our software, please do not hesitate to contact us through
                    our customer service.
                    <br><br>
                    Whether it is a lack of product features, operational inconveniences, or your new expectations and
                    ideas for the product, we take your opinions very seriously. Your feedback will help us continuously
                    improve and perfect our software to better meet your needs.
                    <br><br>
                    Our customer service team is always at your service, looking forward to your feedback and
                    suggestions, and let's work together to create an even better product.
                    <br><br>
                    Thank you for your support and cooperation!
                    <br><br>
                    You can contact us in the following ways:
                    <br><br>
                    Contact Person: HAPGETA SOCIAL Service
                    <br><br>
                    Email: <EMAIL>
                    <br><br>
                </div>


            </div>

        </div>


    </div>
</template>
<style scoped lang="scss">
.container {
    background-color: var(--newsBgColor);
    height: 100%;
    overflow: auto;

    .con {
        /* width: 1200px; */
        width: 80%;
        background-color: #fff;
        margin: 0 auto;
        padding-bottom: 166px;
        padding-top: 50px;

        .con-detail {
            padding: 0 50px;

            .con-detail-text-subtitle {
                font-size: 24px;
                color: var(--mainSubtitleColor);
                line-height: 32px;
                margin: 14px 0;
            }

        }

    }
}

@media (max-width: 768px) {
    .container {
        .con {
            padding-bottom: 82px;
            margin-bottom: 40px;

            .con-detail {
                padding: 0 25px;

                .con-detail-text-subtitle {
                    font-size: 16px;
                    line-height: 24px;
                    margin-bottom: 8px;
                }
            }
        }
    }
}
</style>
