<script setup lang="ts">
import { reactive, ref, defineComponent, defineEmits } from 'vue'
import Confirm from "@/components/confirm/logout.vue"
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router';
import { showDialog } from 'vant';
const router = useRouter()
const dialogVisibleConfirm = ref(false)
const props = defineProps({
    dialogVisibleLogout: {
        type: Boolean,
        require: false,
        default: false
    }

})
// 定义组件的事件
const emits = defineEmits(['dialogCloseLogout'])
const handleClose = () => {
    // 触发自定义事件来通知父组件关闭弹窗
    emits('dialogCloseLogout', false)
}
// 点击注销 开启确认按钮
const logout = () => {
    dialogVisibleConfirm.value = true
    console.log(dialogVisibleConfirm.value)

}
// 确认按钮关闭
const dialogCloseConfirm = () => {
    dialogVisibleConfirm.value = false
}
// 确认注销
const confirmLogout = () => {
    dialogVisibleConfirm.value = false
    //  
    emits('dialogCloseLogout', false)
    showDialog({
        message: 'Cancellation successful',
    }).then(() => {
        // on close
    });
}
const goBack = () => {
    router.go(-1)
}
</script>

<template>
    <div class="container">

        <div class="con">
            <div class="top">
                <div class="left" @click="goBack"><img src="@/assets/images/active/back.png" alt=""></div>
                <div class="center"><img src="@/assets/images/active/delete.png" alt="">
                    <div class="title">Delete your application account</div>
                </div>
                <div></div>

            </div>
            <div class="main">
                <div class="info warn">
                    1) This part is very important, please read carefully: You are trying to delete your application account (you can use various in-app services with this account). After deletion, you will no longer be able to use any in-app related services, and your account and data will also be lost.
                </div>
                <div class="info">2) The following content will be deleted: The list below may not include all services in the applications affected by the deletion of your account (for example, it does not include services that are no longer supported by the application). After deleting the account, your data in these services will also be deleted.
                </div>
                <div class="info">3) The system will delete your created character data, friend data, personal form introductions, communication records, chat text data records, and other data.</div>
                <div class="info">4) Regarding the props and digital content you purchased on "HAPGETA SOCIAL", you will no longer be able to access the content you purchased on "HAPGETA SOCIAL", including clothing, props, plugins, and in-app purchases.</div>
                <div class="end">Currently, there are financial matters in your account. You can contact us (email address: <EMAIL>) for processing.</div>
            </div>

            <div class="btn">
                <van-button type="primary" @click="logout">Yes, I will permanently delete this account and related data.</van-button>
                <!-- <el-button color="rgba(33, 95, 215, 1)" @click="logout"> 是的，我将永久删除次账号及相关数据</el-button> -->
            </div>
        </div>


        <Confirm :dialogVisibleConfirm="dialogVisibleConfirm" @dialogCloseConfirm="dialogCloseConfirm"
            @confirmLogout="confirmLogout" />
    </div>
</template>
<style scoped lang="scss">
.container {
    padding: 0 40px;
    background: #ECF5FF;
    height: 100%;

    .con {
        display: flex;
        flex-direction: column;
        height: 100%;
        align-items: center;
        font-size: 30px;


        .top {
            width: 100%;
            height: 88px;
            display: flex;
            margin-bottom: 34px;
            font-size: var(--fontSize22);
            align-items: center;


            .left {
                img {
                    width: 48px;
                    height: 48px;
                }
            }

            .center {
                display: flex;
                align-items: center;
                justify-content: center;
                flex: 1;
                padding-right: 48px;

                .title {
                    font-weight: var(--fontWeight5);
                    color: rgba(0, 0, 0, 0.8);
                    line-height: 42px;
                    font-size: 34px;
                    font-weight: 500;
                }

                img {
                    width: 38px;
                    height: 38px;
                    margin-right: 16px;
                }
            }



        }

        .main {
            flex: 1;
            overflow-y: auto;

            .info {
                color: rgba(0, 0, 0, 0.80);
                line-height: 48px;
                margin-bottom: 50px;
            }

            .warn {
                color: rgba(255, 23, 23, 0.8);
            }

            .end {
                color: rgba(33, 95, 215, 1);
                margin-bottom: 123px;
            }
        }

        .btn {
            margin: 62px 0 16px;
            height: 88px;
            width: 100%;
        }




        :deep(.van-button) {
            height: 88px;
            width: 100%;
            background: #215FD7;
            font-size: 30px;
        }
    }
}

// @import "@/assets/css/media-queries.scss";</style>
