<script setup lang="ts">
import { ref, computed } from 'vue'
const defaultWay = ref(true)
const num = ref()
const resultNum = ref()
const changeMask = () => {
    defaultWay.value = !defaultWay.value
    num.value = 0
    resultNum.value = 0
}
const changeNum = (data: string) => {
    if (defaultWay.value) {
        if (data == 'add') {
            num.value++
        } else {
            num.value--
        }
        resultNum.value = num.value * 10
    } else {
        if (data == 'add') {
            num.value = num.value + 10
        } else {
            num.value = num.value - 10
        }
        resultNum.value = Math.floor(num.value / 10)
    }


}
const handleInput = () => {
    if (defaultWay.value) {
        let value = num.value
        value = Math.trunc(value)
        //输入位数限制
        console.log(value)
        num.value = value
        resultNum.value = num.value * 10
    } else {
        let value = num.value
        value = Math.floor(value / 10)
        resultNum.value = value
        num.value = value * 10

    }

}

</script>

<template>
    <div class="container">
        <div class="con-currency">
            <div class="titles">
                <img src="@/assets/images/currency/title.png" />
            </div>
            <div class="exchangeWay">
                <div class="mask" :class="[defaultWay ? '' : 'active']">
                    <img src="@/assets/images/currency/tiancai.png" />
                    <div class="change" @click="changeMask"></div>
                    <img src="@/assets/images/currency/g.png" />
                </div>
            </div>
            <div class="quantity">
                <img src="@/assets/images/currency/add.png" @click="changeNum('add')" />
                <div>
                    <el-input v-model="num" type="number" @blur="handleInput" placeholder="Please enter the quantity" />
                </div>
                <img src="@/assets/images/currency/reduced.png" @click="changeNum('reduced')" />
            </div>
            <div class="obtain">
                <div class="info">Obtain quantity ：</div>
                <img src="@/assets/images/currency/g.png" />
                <div class="result">{{ resultNum }}</div>
            </div>
            <div class="btn">confirm </div>
        </div>
        <div class="footer">
            <div> For every new invited user to download "XXX" and successfully registered, the new user will
                receive "<span> 10000 SPACE COIN </span> ", </div>
            <div>and the inviter will receive "<span>10000 SPACE COIN</span>"</div>
        </div>
    </div>
</template>
<style scoped lang="scss">
@import '@/assets/css/index/indexBg.scss';
@import "@/assets/css/login/dialog.css";

.container {
    background: url('@/assets/images/currency/beijing .png') ;
    .footer {
        margin-top: 50px;
        font-size: 16px;
        color: rgba(255, 255, 255, 0.79);
        line-height: 30px;
        text-align: center;

        span {
            color: rgba(84, 224, 249, 1);
        }
    }

    .con-currency {
        display: flex;
        width: 1200px;
        height: 706px;
        margin: 106px auto 0;
        background: url('@/assets/images/currency/conBg.png') no-repeat;
        background-size: 100% 100%;
        flex-direction: column;
        align-items: center;

        .titles {
            height: 70px;
            margin: 55px 0 67px;

            img {
                width: 100%;
            }


        }

        .exchangeWay {
            width: 486px;
            height: 106px;
            background: url('@/assets/images/currency/wayBg.png') no-repeat;
            background-size: 100% 100%;

            .mask {
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: space-between;
                align-items: center;

                img {
                    width: 66px;
                    height: 66px;
                    margin: 0 20px 0 13px;
                }

                .change {
                    flex: 1;
                    width: 100%;
                    height: 100%;
                }
            }

            .active {
                flex-direction: row-reverse;
            }


        }

        .quantity {
            width: 486px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 102px;

            div {
                flex: 1;

            }

            img {
                width: 46px;
                height: 46px;
                margin: 0 14px;
            }
        }
        
        .obtain {
            display: flex;
            align-items: center;
            margin: 40px 0 16px;

            .info {
                font-size: var(--fontSize14);
                color: rgba(255, 255, 255, 0.7);
                line-height: 20px;
            }

            img {
                width: 30px;
                height: 30px;
                margin: 0 2px 0 5px;
            }

            .result {
                font-size: var(--fontSize16);
                color: #FFFFFF;
                line-height: 24px;
            }
        }

        .btn {
            width: 288px;
            height: 98px;
            line-height: 98px;
            text-align: center;
            background: url('@/assets/images/currency/btnBg.png') no-repeat;
            background-size: 100% 100%;
            font-size: var(--fontSize30);
            font-weight: var(--fontWeight5);

        }
    }
}

/* el-input */
:deep(.el-input__inner) {
    height: 42px;
}

:deep(.el-input) {
    margin-bottom: 0;
}

:deep(.el-input__wrapper) {
    background: url('@/assets/images/currency/input.png') no-repeat;

}
@import "@/assets/css/media-queries.scss";
</style>
