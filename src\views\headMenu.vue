<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { showToast } from 'vant';
import useCounterStore from "@/stores/counter"
import { useRouter } from 'vue-router';
import Login from "@/views/login/index.vue"
import Forget from "@/views/login/forget.vue"
import Logout from "@/views/login/logout.vue"
import { quitAccount } from "@/api/index"
const router = useRouter()
const countStore = useCounterStore()
import { getCurrentInstance } from 'vue'
const instance = getCurrentInstance()
let headList = ref([
    { title: 'Home', path: '/index' },
    { title: 'News', path: '/newsDynamic' },
    { title: 'Introduction', path: '/index' },
    { title: 'Features', path: '/index' },
    // { title: 'Recharge', path: '/index' },

])
const headMenu = ref<HTMLElement | null>(null);
const dialogVisible = ref(false)
const dialogVisibleForget = ref(false)
const dialogVisibleLogout = ref(false)
const showContent = ref(false)  // 初始化为不显示
const timer = ref()
onMounted(() => {
    countStore.headMenuDomTop = (headMenu.value as HTMLElement).clientHeight

})
const changeRouter = (data: number) => {
    console.log(data)
    // 判断是否在首页
    if (instance) {
        // 获取路由信息
        const router = instance.appContext.config.globalProperties.$router;
        const route = instance.appContext.config.globalProperties.$route;
        // 判断是否在首页
        if (route.path === '/index') {
            // console.log('当前在首页');
            countStore.tabActive = data
            countStore.isClick = true
            // console.log(countStore.isClick) 
            // if (data == 4) {
            //     router.push({ path: '/recharge' })
            // }
        } else {
            console.log('当前不在首页');
            router.replace({path:'/index'})
            countStore.tabActive = 4
            setTimeout(()=>{
                countStore.tabActive = data
                countStore.isClick = true
            },300)
        }
    }
}
// export default defineComponent({
//     name: "headMenu",
//     setup() {

//         const name = ref("sss");//利用ref函数实现双向绑定，name指向一个ref对象
//         let count = 0     //模板中不需要使用，故不暴露出去
//         const changeName = function () {//定义函数表达式
//             count++
//             name.value = "哇塞" + count//获取name的值，必须要用name.value来获取
//         }
//         return {//必须要return暴露出去
//             name,
//             changeName,
//             headList,
//             current,
//             changeRouter
//         }
//     }
// })
// 点击登录
const openDialog = (data: boolean) => {
    dialogVisible.value = data
}
// 收到登录页面关闭
const dialogClose = (data: boolean) => {
    dialogVisible.value = data
}
// 点击忘记密码
const dialogForget = (data: boolean) => {
    // dialogVisible.value = false //登录页面是否关闭
    dialogVisibleForget.value = data
}
// 收到忘记密码页面关闭
const dialogCloseForget = (data: boolean) => {
    dialogVisibleForget.value = data
}
// 点击注销账号
const openDialogLogout = (data: boolean) => {
    router.push({ path: '/login/delete' })
    // dialogVisibleLogout.value = data
}
// 
const dialogCloseLogout = (data: boolean) => {
    dialogVisibleLogout.value = data
}
// 
const startTimer = () => {
    showContent.value = true// 将showContent设置为true，显示内容
    setTimeout(() => {
        showContent.value = false; // 5000ms（5秒）后再次将showContent设置为false，隐藏内容
    }, 2000);
}
const stopTimer = () => {
    // console.log('lia')
    // clearTimeout() // 清除之前设置的定时器
}
// on为订阅事件
// on接受第一个参数为事件名（同发布事件名）,通配符*为监听所有，第二个参数接受一个回调
instance?.proxy?.emitter.on('mitt-logout', (params) => {
    console.log(params);
    dialogVisibleLogout.value = true
})
instance?.proxy?.emitter.on('mitt-delete', (params) => {
    dialogVisible.value = true
})
// instance?.proxy?.emitter.on('*',()=>{ console.log('监听所有事件') })
onBeforeUnmount(() => {
    instance?.proxy?.emitter.off('mitt-logout')
    instance?.proxy?.emitter.off('mitt-delete')
    // instance?.proxy?.emitter.all.clear()    // all方法全部解绑
})
// 退出登录
const account = async () => {
    console.log('2222')
    try {
        const res = await quitAccount(localStorage.getItem('id'));
        console.log(res)
        if (res.code == 0 || res.code == 1) {
            // 注销成功
            localStorage.clear();
            countStore.login = '';
            showToast({
                message: 'Logout',
            });
        } else if (res.code == 5) {
            // 用户不存在
            showToast({
                message: 'User does not exist.',
            });
            localStorage.clear();
            countStore.login = '';
        } else {
            // 修改失败
            showToast({
                message: 'Logout failed',
            });
        }
    } catch (error) {
        console.error('Fetch error:', error);
    }
}
</script>

<template>
    <div class="header" ref="headMenu">
        <div class="header-list">
            <div class="header-top">
                <div class="logo">
                    <img src="@/assets/images/index/logo.png">
                </div>
                <ul class="header-menu">
                    <li v-for="(item, i) in headList" :key="i" :class="countStore.tabActive == i ? 'activeList' : ''"
                        @click="changeRouter(i)">
                        <!-- <RouterLink :to="item.path">
                            <span>{{ item.title }}</span>
                        </RouterLink> -->
                        <span>{{ item.title }}</span>
                    </li>
                </ul>

                <div class="login">
                    <div v-if="countStore.login" class="user" @mouseenter="startTimer" @mouseleave="stopTimer">
                        <div class="account">{{ countStore.login }}</div>
                        <div class="toolTip" :class="[showContent ? 'showCon' : '']">
                            <div class="quit" @click="account()">Logout</div>
                            <div class="logout" @click="openDialogLogout(true)">Delete Account</div>
                        </div>
                    </div>
                    <div v-else class="loginBtn" @click="openDialog(true)">Login</div>
                    <!-- <div class="logout" @click="openDialogLogout(true)">注销账号</div>
                    <div class="quit">退出登录</div> -->

                </div>
            </div>

        </div>
    </div>
    <Login :dialogVisible="dialogVisible" @dialogClose="dialogClose" @dialogForget="dialogForget" />
    <Forget :dialogVisibleForget="dialogVisibleForget" @dialogCloseForget="dialogCloseForget" />
    <!-- <Logout :dialogVisibleLogout="dialogVisibleLogout" @dialogCloseLogout="dialogCloseLogout" /> -->
</template>
<style>
.el-popper {
    font-size: 15px;
    padding: 0 42px;
    line-height: 37px;
}

.el-popper .is-dark {
    background: rgba(11, 11, 11, 0.56);

}
</style>
<style scoped lang="scss">
.header {
    height: 80px;
    font-size: var(--fontSize20);

    .header-list {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 80px;
        background: var(--bgColor);
        z-index: 999;
    }

    .header-top {
        // width: 1920px;
        height: 80px;
        margin: auto;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #fff;

        .logo {
            display: flex;
            align-items: center;
            // margin: 0 250px 0 50px;
            margin-left: 50px;

            img {
                width: 240px;
                height: 50px;
            }

        }

        .header-menu {
            flex: 1;
            display: flex;
            justify-content: center;
            list-style: none;
            align-items: center;
            height: 100%;

            li {
                width: 186px;
                position: relative;
                height: 100%;
                text-align: center;
                line-height: 80px;

                a {
                    text-decoration: none;
                    height: 100%;
                    width: 100%;
                    padding: 25px 50px
                }

                span {
                    font-weight: 400;
                    color: #fff;
                    height: 100%;
                }


            }

            // li:hover {
            //     // background: url("@/assets/images/index/popup_slider.png");

            //     // span {
            //     //     font-weight: 500;
            //     //     color: var(--activeColor);
            //     // }
            // }

            .activeList {
                background: url("@/assets/images/index/popup_slider.png");

                span {
                    font-weight: 500;
                    color: var(--activeColor);
                }
            }

        }

        .login {
            display: flex;
            justify-content: center;
            font-size: 15px;
            color: #fff;
            position: relative;

            .loginBtn {
                margin-right: 30px;
            }

            .user {
                // font-size: 16px;
                color: #FFFFFF;
                line-height: 24px;
                margin-right: 30px;

                .account {
                    font-size: 10px;
                }

                .toolTip {
                    position: absolute;
                    top: 36px;
                    right: 30px;
                    display: none;
                    width: 144px;
                    height: 100px;
                    background: rgba(11, 11, 11, 0.56);
                    border-radius: 4px;
                    color: #fff;
                    text-align: center;

                    .quit {
                        margin: 24px 0 16px;
                    }

                    div:hover {
                        color: var(--activeColor);
                    }

                }

                .showCon {
                    display: block;
                }

            }



            // .user:hover .toolTip {
            //     display: block;
            // }



            // div {
            //     margin-right: 30px;
            // }

        }




    }
}


// @media (max-width:760px) {
//     .header .header-top .header-menu li span {
//         font-size: var(--fontSize14);
//     }

//     .header .header-top .logo img {
//         width: 93px;
//         height: 20px;
//     }

//     .header .header-top .header-menu li {
//         width: 80px;
//     }
// }
// @media (min-width: 760px) and (max-width: 1200px) {
//     .header .header-top .header-menu li span {
//         font-size: var(--fontSize16);
//     }

//     .header .header-top .logo img {
//         width: 93px;
//         height: 20px;
//     }

//     .header .header-top .header-menu li {
//         width: 100px;
//     }
// }
@import "@/assets/css/media-queries.scss";
</style>