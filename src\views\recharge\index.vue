<template>
    <div class="container">
        <div class="con">
            <el-row class="tac">
                <el-col>
                    <el-menu :default-active="activeMenu" :router="true" class="el-menu-vertical-demo"
                        text-color=" rgba(0,0,0,0.65)" active-text-color="#215FD7" @open="handleOpen"
                        @close="handleClose">
                        <el-menu-item index="/recharge/rechargeCenter">
                            <span>充值中心</span>
                        </el-menu-item>
                        <el-menu-item index="/recharge/rechargeRecord">
                            <span>充值记录</span>
                        </el-menu-item>
                    </el-menu>
                </el-col>
            </el-row>
            <div class="menuCon">
                <router-view></router-view>
            </div>

        </div>
    </div>
</template>
<script setup lang="ts">
import { ref, nextTick, onMounted } from 'vue'
import { useRouter } from 'vue-router';
const router = useRouter()
const dataList = ref(['1', '8', '10', '15'])
const current = ref(0)
const value = ref('')
const activeMenu = ref('/recharge/rechargeCenter'); // 默认激活的菜单项
const handleOpen = (key: string, keyPath: string[]) => {
    console.log(keyPath)

}
const handleClose = (key: string, keyPath: string[]) => {
    console.log(key, keyPath)
}
const handleInput = (event) => {
    let value = event;
    current.value = null
    console.log(value)
    if (value > 300) {
        nextTick(() => {
            value.value = 300
            amount.value = 300
        })
    } else {
        amount.value = value
    }
}

</script>
<style scoped lang="scss">
.container {
    background-color: var(--newsBgColor);
    /* background-size: 100% 100%; */
    height: 100%;
    overflow: auto;
    width: 100%;
    color: rgba(0, 0, 0, 0.7);

    .con {
       width: 1200px;
        height: 560px;
        background: #FFFFFF;
        // filter: blur(10px);
        margin: 0 auto;
        margin-top: 100px;
        display: flex;

        .el-row {
            width: 186px;
            height: 100%;
            border-right: 1px solid #E9E9E9;
            padding-top: 22px;

            .el-menu {
                border-right: none;
            }

            .el-menu-item.is-active {
                background: #DFE8FF;
            }
        }

        .menuCon {
            flex: 1;
            height: 100%;
            overflow: hidden;
        }

    }


}

@media (max-width: 768px) {}
</style>