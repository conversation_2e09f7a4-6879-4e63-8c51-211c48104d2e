<script setup lang="ts">
import { reactive, ref, defineComponent, defineEmits } from 'vue'
import Confirm from "@/components/confirm/logout.vue"
import { ElMessage } from 'element-plus'
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
import { getCurrentInstance } from 'vue';
const instance = getCurrentInstance()
const dialogVisibleConfirm = ref(false)
const props = defineProps({
    dialogVisibleLogout: {
        type: Boolean,
        require: false,
        default: false
    }

})
// 定义组件的事件
const emits = defineEmits(['dialogCloseLogout'])
const handleClose = () => {
    // 触发自定义事件来通知父组件关闭弹窗
    emits('dialogCloseLogout', false)
}
// 点击注销 开启确认按钮
const logout = () => {
    // emits('dialogCloseLogout', false)
    // 判断是否登录
    if(countStore.login){
        // 弹出二次弹框确认注销
        dialogVisibleConfirm.value = true
    }else{
        // 去登陆
        instance?.proxy?.emitter.emit('mitt-delete', true)
    }
}
// 确认按钮关闭
const dialogCloseConfirm = () => {
    dialogVisibleConfirm.value = false
}
// 确认注销
const confirmLogout = () => {
    dialogVisibleConfirm.value = false
    //  
    emits('dialogCloseLogout', false)
    // ElMessage('注销成功')
}
</script>

<template>
    <div class="container">
        <!-- <el-dialog :model-value="props.dialogVisibleLogout" title="" width="40%" align-center @close="handleClose"> -->
        <div class="con">
            <div class="top">
                <img src="@/assets/images/active/delete.png" alt="">
                <div class="title">Delete your application account</div>
            </div>
            <div class="textCon">
                <div class="info warn">
                    1) This part is very important, please read carefully: You are trying to delete your application
                    account (you can use various in-app services with this account). After deletion, you will no longer
                    be able to use any in-app related services, and your account and data will also be lost.
                </div>
                <div class="info">2) The following content will be deleted: The list below may not include all services
                    in the applications affected by the deletion of your account (for example, it does not include
                    services that are no longer supported by the application). After deleting the account, your data in
                    these services will also be deleted.
                </div>
                <div class="info">3) The system will delete your created character data, friend data, personal form
                    introductions, communication records, chat text data records, and other data.</div>
                <div class="info">4) Regarding the props and digital content you purchased on "HAPGETA SOCIAL", you will
                    no longer be able to access the content you purchased on "HAPGETA SOCIAL", including clothing,
                    props, plugins, and in-app purchases.</div>
            </div>
            <div class="end">Currently, there are financial matters in your account. You can contact us (email address:
                <EMAIL>) for processing.</div>
            <div class="btn">
                <el-button color="rgba(33, 95, 215, 1)" @click="logout"> Yes, I will permanently delete this account and
                    related data.</el-button>
            </div>
        </div>

        <!-- </el-dialog> -->
        <Confirm :dialogVisibleConfirm="dialogVisibleConfirm" @dialogCloseConfirm="dialogCloseConfirm"
            @confirmLogout="confirmLogout" />
            
    </div>
</template>
<style scoped lang="scss">
:deep(.el-dialog) {
    background: linear-gradient(231deg, #F4F9FF 0%, #EBF4FF 100%);
    border-radius: 18px;
}

:deep(.el-dialog__header) {
    text-align: center;
}

.container {
    background: linear-gradient(231deg, #F4F9FF 0%, #EBF4FF 100%);
    padding: 100px 0;
}

.con {
    width: 50%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 16px;

    .top {
        display: flex;
        margin-bottom: 20px;
        font-size: var(--fontSize22);
        align-items: center;

        img {
            width: 28px;
            height: 28px;
            margin-right: 9px;
        }

        .title {
            font-weight: var(--fontWeight5);
            color: rgba(0, 0, 0, 0.8);
            line-height: 27px;
        }

    }

    .info {
        text-align: left;
        color: rgba(0, 0, 0, 0.80);
        line-height: 30px;
        margin-bottom: 20px;
    }

    .warn {
        color: rgba(255, 23, 23, 0.8);
    }

    .end {
        color: rgba(33, 95, 215, 1);
        margin-bottom: 123px;
    }

    :deep(.el-button) {
        height: 48px;
    }
}

@import "@/assets/css/media-queries.scss";

@media (max-width: 768px) {
    :deep(.el-dialog) {
        width: 80%;
    }

    .con {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 14px;

        .top {
            display: flex;
            margin-bottom: 10px;
            font-size: var(--fontSize22);

            img {
                width: 18px;
                height: 18px;
                margin-right: 9px;
            }

            .title {
                font-size: 16px;
                font-weight: var(--fontWeight5);
                line-height: 14px;
            }

        }

        .info {
            line-height: 15px;
            margin-bottom: 10px;
        }

        .end {
            margin-bottom: 60px;
        }

        :deep(.el-button) {
            height: 28px;
        }
    }
}

@media (min-width: 769px) and (max-width: 1200px) {
    :deep(.el-dialog) {
        width: 60%;
    }
}
</style>
