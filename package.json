{"name": "proj-metauniverse-vue3", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --mode development", "test": "vite build --mode test", "build": "vite build --mode production", "pre": "vite build --mode pre", "build-only": "vite build", "type-check": "vue-tsc --build --force", "build:mobile": "NODE_ENV=mobile webpack --config webpack.config.js", "build:web": "NODE_ENV=web webpack --config webpack.config.js"}, "dependencies": {"axios": "^1.6.5", "buffer": "^6.0.3", "element-plus": "^2.4.4", "lodash": "^4.17.21", "mitt": "^3.0.1", "pinia": "^2.1.7", "postcss-plugin-px2rem": "^0.8.1", "postcss-px-to-viewport-8-with-include": "^1.2.2", "qrcode": "^1.5.4", "sass": "^1.69.7", "sass-loader": "^13.3.3", "twitter-login": "^1.1.5", "vant": "^4.9.4", "vue": "^3.3.11", "vue-clipboard-next": "^1.0.0", "vue-clipboard3": "^2.0.0", "vue-router": "^4.2.5", "vue3-google-login": "^2.0.33"}, "devDependencies": {"@tsconfig/node18": "^18.2.2", "@types/node": "^18.19.4", "@vitejs/plugin-vue": "^4.5.2", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/tsconfig": "^0.5.0", "npm-run-all2": "^6.1.1", "postcss": "^8.4.41", "postcss-px-to-viewport": "github:evrone/postcss-px-to-viewport", "postcss-px-to-viewport-8-plugin": "^1.2.5", "typescript": "~5.3.0", "vite": "^5.0.10", "vue-tsc": "^1.8.25"}}