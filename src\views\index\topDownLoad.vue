<template>
    <div class="container topDownLoadBg">
        <div class="download">
            <div class="twoCode">
                <img src="@/assets/images/index/downCode.png">
            </div>
            <div class="download-list">
                <div class="download-item" v-for="item in downloadList" :key="item.id"
                    :style="{ background: item.color }" :class="[item.id == 1 ? 'otherMargin' : '']"
                    @click="downBtn(item.id)">
                    <!-- <img :src="`/src/assets/images/index/${item.url}`"> -->
                    <img :src="item.url">
                    <div class="text">{{ item.title }}</div>
                </div>

            </div>

        </div>
    </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import pingguo from '@/assets/images/index/pingguo 2.png'
import GooglePlay from '@/assets/images/index/a-GooglePlay 2.png'
import anzhuo from '@/assets/images/index/anzhuo 2.png'
// { id: 2, url: anzhuo, title: 'Android Download' ,color:' #4CBDFF'}
const downloadList = [{ id: 0, url: pingguo, title: 'App store Download', color: '#141414' }, { id: 1, url: GooglePlay, title: 'Google play Download', color: '#56C366' }];
const downBtn = ((data:number) => {
    if (data == 0) {
        window.location.href = 'https://apps.apple.com/us/app/hapmeta-social/id6738371086'
    } else if (data == 1) {
        window.location.href = 'https://play.google.com/store/apps/details?id=com.geniusmetaltd.hapmetasocial'
    }
})
</script>


<style scoped lang="scss">
.container {
    // height: auto;
    height: 900px;
    background: url('@/assets/images/index/banner.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column-reverse;
    align-items: center;

    .download {
        margin-bottom: 95px;
        // width: 638px;
        height: 172px;
        background: url('@/assets/images/index/downLoadBg.png') no-repeat;
        background-size: 100% 100%;
        display: flex;

        .twoCode {
            width: 122px;
            height: 122px;
            margin: 21px 14px 21px 67px;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .download-list {
            margin: 21px 0;
            display: grid;
            // grid-template-columns: repeat(2, 1fr);
            gap: 14px;

            .download-item {
                width: 180px;
                height: 58px;
                background: #141414;
                border-radius: 8px;
                display: flex;
                align-items: center;
                margin-right: 67px;

                img {
                    margin: 0 10px 0 20px;
                    width: 34px;
                    height: 34px;
                }

                .text {
                    font-size: 18px;
                    color: #FFFFFF;
                    line-height: 24px;
                }
            }

            .otherMargin {
                img {
                    margin: 0 7px 0 14px;
                }
            }
        }

    }

}

@import "@/assets/css/media-queries.scss";
</style>
