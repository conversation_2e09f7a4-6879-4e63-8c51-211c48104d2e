<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { useRouter } from 'vue-router';
const router = useRouter()
import { ElTable } from 'element-plus'

interface User {
    date: string
    name: string
    address: string
}

const currentRow = ref()
const singleTableRef = ref<InstanceType<typeof ElTable>>()

const setCurrent = (row?: User) => {
    singleTableRef.value!.setCurrentRow(row)
}
const handleCurrentChange = (val: User | undefined) => {
    currentRow.value = val
}
const tableData: User[] = [
    {
        order: '098002233',
        num: '100天才币',
        price: '$6',
        time: '2017-10-01 14:10',
        pay: '银联',
        state: '未兑换',
        code: '098002233'
    },
    {
        order: '098002233',
        num: '100天才币',
        price: '$6',
        time: '2017-10-01 14:10',
        pay: '银联',
        state: '未兑换',
        code: '098002233'
    },
    {
        order: '098002233',
        num: '100天才币',
        price: '$6',
        time: '2017-10-01 14:10',
        pay: '银联',
        state: '未兑换',
        code: '098002233'
    },
    {
        order: '098002233',
        num: '100天才币',
        price: '$6',
        time: '2017-10-01 14:10',
        pay: '银联',
        state: '未兑换',
        code: '098002233'
    },
    {
        order: '098002233',
        num: '100天才币',
        price: '$6',
        time: '2017-10-01 14:10',
        pay: '银联',
        state: '未兑换',
        code: '098002233'
    },
    {
        order: '098002233',
        num: '100天才币',
        price: '$6',
        time: '2017-10-01 14:10',
        pay: '银联',
        state: '未兑换',
        code: '098002233'
    },
    {
        order: '098002233',
        num: '100天才币',
        price: '$6',
        time: '2017-10-01 14:10',
        pay: '银联',
        state: '未兑换',
        code: '098002233'
    },
    {
        order: '098002233',
        num: '100天才币',
        price: '$6',
        time: '2017-10-01 14:10',
        pay: '银联',
        state: '未兑换',
        code: '098002233'
    },
]
const headerCellStyle = ({ rowIndex }) => {
    if (rowIndex === 0) {
        return {
            backgroundColor: '#FAFAFA', // 设置表头背景颜色
        };
    }
    return {};
};
</script>

<template>

    <div class="recharge">
        <div class="title">充值记录</div>
        <div class="rechargeCon">
            <el-table ref="singleTableRef" cell-class-name="custom-table" :data="tableData" highlight-current-row
                :header-cell-style="headerCellStyle" style="width: 100%" @current-change="handleCurrentChange">
                <el-table-column type="index" label="序号" width="80px" />
                <el-table-column property="order" label="订单号" />
                <el-table-column property="num" label="充值数量" />
                <el-table-column property="price" label="充值价格" />
                <el-table-column property="time" label="充值时间" width="150px" />
                <el-table-column property="pay" label="支付方式" />
                <!-- <el-table-column label="兑换状态" width="130px">
                    <template #default="scope">
                        <div class="state"></div>
                        <span>{{ scope.row.code }}</span>
                        <span>{{ scope.row.codeLabel }}</span>

                    </template>
                </el-table-column>
                <el-table-column label="兑换码" width="200px">
                    <template #default="scope">
                        <span>{{ scope.row.code }}</span>
                        <span>{{ scope.row.codeLabel }}</span>

                        <div class="copy">复制</div>
                    </template>
                </el-table-column> -->
            </el-table>
            <div class="pagination">
                <el-pagination background layout="prev, pager, next" :total="1000" />
            </div>

        </div>
    </div>
</template>
<style scoped lang="scss">
.recharge {
    margin: 22px 32px;

    .title {
        font-weight: 500;
        font-size: 20px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 29px;
        margin-bottom: 12px;
    }

    .rechargeCon {

        .pagination {
            margin: 32px auto;
            display: flex;
            justify-content: center;
        }

        .custom-table {
            height: 53px;
            /* 设置行高 */
        }

        :deep(.el-table .el-table__cell) {
            // padding: 0;
        }

        :deep(.el-table .cell) {
            display: flex;
            align-items: center;
        }

        .state {
            width: 6px;
            height: 6px;
            background: #1890FF;
            border-radius: 50%;
            margin-right: 8px;
        }

        .copy {
            width: 70px;
            height: 22px;
            line-height: 22px;
            text-align: center;
            background: #DFE8FF;
            border-radius: 2px;
            font-size: 14px;
            color: #215FD7;
            margin-left: 16px;
        }
    }

}
</style>