<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router';
const router = useRouter()
const activeIndex = ref('1')
interface NewsItem {
  con: string;
  title: string;
}
const dataList = ref<NewsItem[]>([])
const handleSelect = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}
const gotoDetail = () => {
  router.push({ path: '/newsDynamic/detail' })
}
</script>

<template>
  <div class="container">
    <div class="banner">
      <img src="@/assets/images/index/banner (1).png" />
      <div class="con">
        <div class="con-title">News</div>
        <div class="con-newsBanner">
          <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect">
            <el-menu-item index="1">News</el-menu-item>
            <el-menu-item index="2">Activity</el-menu-item>
            <el-menu-item index="3">Notice</el-menu-item>
          </el-menu>
        </div>
        <div class="con-list">
          <div class="con-list-item" v-for="(item, index) in dataList" :key="index" @click="gotoDetail">
            <el-tag>新闻</el-tag>
            <div class="con-list-item-con">
              <div class="title">{{ item.title }}</div>
              <div class="info">
                {{ item.con }}
              </div>
            </div>
          </div>
        </div>
        <div class="page" v-if="dataList.length">
          <el-pagination background layout="prev, pager, next" :pager-count="3" next-text="Next page"
            prev-text="Previous page" :total="1000" />
        </div>

      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.container {
  background-color: var(--newsBgColor);
  /* background-size: 100% 100%; */
  height: 100%;
  overflow: auto;

  .banner {
    img {
      width: 100%;
    }
  }

  .con {
    width: 70%;
    background-color: #fff;
    margin: 0 auto;
    padding-bottom: 100px;

    .con-title {
      text-align: center;
      font-size: 40px;
      font-weight: 500;
      color: #000000;
      line-height: 58px;
      padding-top: 46px;
    }

    .con-newsBanner {
      margin: 0 50px;

      /* 取消触摸背景色 */
      :deep(.el-menu-item:hover) {
        background-color: #fff;
      }

      :deep(.el-menu--horizontal) {
        justify-content: center;
      }

    }

    .con-list {
      margin: 39px 50px 0;

      .con-list-item {
        display: flex;
        /* height: 149px; */
        border-bottom: 1px solid rgba(217, 217, 217, 0.33);
        margin-bottom: 40px;

        .con-list-item-con {
          margin-left: 29px;

          .title {
            font-size: 22px;
            color: #333333;
            /* line-height: 32px; */
            letter-spacing: 1px;
            margin-bottom: 16px;
          }

          .info {
            font-size: 16px;
            color: rgba(51, 51, 51, 0.7);
            line-height: 24px;
            letter-spacing: 1px;
            -webkit-text-stroke: 1px #979797;
            text-stroke: 1px #979797;
            margin-bottom: 47px;
          }
        }
      }
    }


  }

  :deep(.el-pagination.is-background .el-pager li.is-active) {
    background-color: #979797;
    color: #fff;
  }

  :deep(.el-pagination.is-background .el-pager li:hover) {
    color: #fff;
    background-color: #979797;
  }

  :deep(.el-pagination.is-background .el-pager li) {
    background-color: #fff;
    border: 1px solid #979797;
    color: rgba(51, 51, 51, 0.7);
  }

  :deep(.el-pager li) {
    font-size: var(--fontSize16);
  }

  /* 上一页 下一页 */
  :deep(.el-pagination.is-background .btn-next) {
    background-color: #fff;
    border: 1px solid #979797;
    color: rgba(51, 51, 51, 0.7);
  }

  :deep(.el-pagination.is-background .btn-next:hover) {
    background: #979797;
    color: #fff;

  }

  /* 上一页 */
  :deep(.el-pagination.is-background .btn-prev) {
    background-color: #fff;
    border: 1px solid #979797;
    color: rgba(51, 51, 51, 0.7);

  }

  :deep(.el-pagination.is-background .btn-prev:hover) {

    background: #979797;
    color: #fff;
  }

  :deep(.el-pagination button) {
    font-size: var(--fontSize16);
  }

  :deep(.el-pagination) {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .container {
    .con {
      width: 85%;

      .con-title {
        text-align: center;
        font-size: 20px;
        font-weight: 500;
        line-height: 28px;
        padding-top: 23px;
      }

      .con-newsBanner {
        margin: 0 20px;

        /* 取消触摸背景色 */

      }

      .con-list {
        margin: 20px 20px 0;

        .con-list-item {
          margin-bottom: 20px;

          :deep(.el-tag) {
            font-size: 10px;
            padding: 0 4px;
            height: 18px;
          }

          .con-list-item-con {
            margin-left: 12px;

            .title {
              font-size: 14px;
              margin-bottom: 6px;
            }

            .info {
              font-size: 12px;
              line-height: 20px;
              margin-bottom: 22px;
            }
          }
        }
      }
    }
  }

  :deep(.el-pagination.is-background .btn-prev) {
    font-size: 12px;
  }

  :deep(.el-pagination.is-background .btn-next) {
    font-size: 12px;
  }

  :deep(.el-pagination.is-background .el-pager li) {
    font-size: 12px;
  }
}
</style>