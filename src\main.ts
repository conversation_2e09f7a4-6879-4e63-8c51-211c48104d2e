import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'
import router from './router'
import mitt from 'mitt'
const Mit = mitt()
import Vant from 'vant';
import 'vant/lib/index.css';
import vue3GoogleLogin from 'vue3-google-login'
const app = createApp(App)
//TypeScript注册
// 由于必须要拓展ComponentCustomProperties类型才能获得类型提示
declare module 'vue' {
    export interface ComponentCustomProperties {
        emitter: typeof Mit
    }
}
// email: "<EMAIL>"
// name: "chunlei peng"
// clientId: '668413462466-6798l5752opm9j49k514sgm08lb9fslr.apps.googleusercontent.com',
app.use(vue3GoogleLogin, {
    clientId: '478856571412-66s4c54lqvohf05g7u3tjkddi52u6vcd.apps.googleusercontent.com',
    // redirectUri:'https://ample-internal-coyote.ngrok-free.app/game-login/callback',
    // scope: 'https%3A//www.googleapis.com/auth/drive.metadata.readonly%20https%3A//www.googleapis.com/auth/calendar.readonly',
    // type: 'redirect',
    // response_type:'token'
})
app.config.globalProperties.emitter = Mit
app.use(ElementPlus)
app.use(createPinia())
app.use(Vant)
app.use(router)
app.mount('#app')
