<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router';
const router = useRouter()
const newsList = ref([{ id: 0, title: 'News' }, { id: 1, title: 'Activity' }, { id: 2, title: 'Notice' }])
import game1 from '@/assets/images/index/swiper01.png'
import game2 from '@/assets/images/index/swiper02.png'
import game3 from '@/assets/images/index/swiper03.png'
const swiperList = [{ id: 0, url: game1 }, { id: 1, url: game2 }, { id: 2, url: game3 }]
interface NewsItem {
    title: string;
    time: string;
}
const dataList = ref<NewsItem[]>([])
const current = ref(0)
const changeNewsList = (i: number) => {
    console.log(i)
    current.value = i
}
const gotoDetail = () => {
    router.push({ path: '/newsDynamic' })
}
</script>

<template>
    <div class="container newContainer">
        <div class="title"></div>
        <!-- web -->
        <div class="con conWeb">
            <div class="con-left">
                <el-carousel>
                    <el-carousel-item v-for="item in swiperList" :key="item">
                        <!-- <img :src="`/src/assets/images/index/${item.url}`" /> -->
                        <img :src="item.url">
                    </el-carousel-item>
                </el-carousel>
            </div>
            <div class="con-right">
                <div class="con-right-top">
                    <div class="con-right-top-left">
                        <div class="con-right-top-item" :class="[current == item.id ? 'activeList' : '']"
                            v-for="item in newsList" :key="item.id" @click="changeNewsList(item.id)">{{ item.title }}
                        </div>
                    </div>
                    <div class="con-right-top-right" @click="gotoDetail">
                        <img src="@/assets/images/index/newsMore.png" />
                    </div>
                </div>
                <!-- 新闻列表 -->
                <div class="con-right-newsList">
                    <div class="newsList-item" v-for="(item, index) in dataList" :key="index">
                        <div class="item-left"></div>
                        <div class="item-con">
                            {{ item.title }}
                            <!-- "Hapmeta Social" will launch advertisements on March 15, 2024. -->
                        </div>
                        <div class="item-time">{{ item.time }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<style scoped lang="scss">
@import '@/assets/css/index/indexBg.scss';

.container {
    background: url('@/assets/images/index/newsBg.png') no-repeat;
    background-size: 100% 100%;

    .title {
        background: url('@/assets/images/index/news.png') no-repeat;
        // width: 530px;
        background-size: 100% 100%;
        width: 40%;
    }

    .conWeb {
        margin-top: 44px;
        display: flex;
        width: 80%;
        height: 100%;

        .con-left {
            flex: 1;
            height: 432px;
            margin-right: 14px;
            overflow: hidden;
            background-color: rgba(0, 0, 0, 0.1);

            img {
                max-width: 100%;
                height: 100%;
                object-fit: contain;
            }

            :deep(.el-carousel--horizontal) {
                height: 100%;
            }

            :deep(.el-carousel__container) {
                height: 100%;
            }
        }

        .con-right {
            flex: 1;
            height: 432px;
            background: url('@/assets/images/index/newsListBg.png') no-repeat;
            background-size: 100% 100%;
            display: flex;
            flex-direction: column;
            overflow: auto;

            .con-right-top {
                height: 46px;
                font-size: 20px;
                display: flex;
                align-items: center;

                .con-right-top-left {
                    flex: 1;
                    display: grid;
                    grid-template-columns: repeat(3, 1fr);

                    .con-right-top-item {
                        position: relative;
                        height: 46px;
                        text-align: center;
                        line-height: 46px;
                        /* width: 140px; */
                        cursor: pointer;
                    }

                    .con-right-top-item:hover {
                        color: var(--activeColor);
                    }

                    .activeList {
                        color: var(--activeColor);
                        border-bottom: 3px solid var(--activeColor);
                        transition: all 0.2s;
                    }

                    /* 伪元素 */
                    .con-right-top-item::before {
                        content: '';
                        position: absolute;
                        bottom: -3px;
                        left: 50%;
                        width: 0;
                        height: 100%;
                        border-bottom: 3px solid var(--activeColor);
                        transition: all 0.2s;
                    }

                    .con-right-top-item:hover::before {
                        content: '';
                        width: 100%;
                        left: 0;
                    }
                }

                .con-right-top-right {
                    img {
                        width: 18px;
                        height: 18px;
                        margin-right: 19px;
                    }
                }

            }

            .con-right-newsList {
                flex: 1;
                margin-top: 17px;
                overflow: auto;

                .newsList-item {
                    display: flex;
                    margin: 20px 21px;
                    font-size: 18px;
                    line-height: 26px;
                    height: 28px;
                    align-items: center;
                    justify-content: center;

                    .item-left {
                        width: 2px;
                        height: 20px;
                        background: #FFFFFF;
                    }

                    .item-con {
                        flex: 1;
                        margin-left: 12px;
                    }
                }
            }
        }
    }
}

@import "@/assets/css/media-queries.scss";
</style>
